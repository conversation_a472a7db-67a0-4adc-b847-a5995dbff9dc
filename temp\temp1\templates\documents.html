{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">المستندات</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('upload') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مستند جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">نوع المستند</label>
                    <select class="form-select" name="document_type">
                        <option value="">الكل</option>
                        <option value="incoming">وارد</option>
                        <option value="outgoing">صادر</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">التصنيف</label>
                    <select class="form-select" name="category">
                        <option value="">الكل</option>
                        <option value="official">رسمي</option>
                        <option value="internal">داخلي</option>
                        <option value="external">خارجي</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">الكل</option>
                        <option value="pending">قيد المعالجة</option>
                        <option value="in_progress">جاري المعالجة</option>
                        <option value="completed">تم المعالجة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">التاريخ</label>
                    <input type="date" class="form-control" name="date">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Documents Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>التصنيف</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>المسؤول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in documents|default([]) %}
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="{{ doc.id }}">
                                </div>
                            </td>
                            <td>{{ doc.title }}</td>
                            <td>
                                {% if doc.document_type == 'incoming' %}
                                <span class="badge bg-primary">وارد</span>
                                {% else %}
                                <span class="badge bg-success">صادر</span>
                                {% endif %}
                            </td>
                            <td>{{ doc.category }}</td>
                            <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if doc.status == 'pending' %}
                                <span class="badge bg-warning">قيد المعالجة</span>
                                {% elif doc.status == 'in_progress' %}
                                <span class="badge bg-info">جاري المعالجة</span>
                                {% elif doc.status == 'completed' %}
                                <span class="badge bg-success">تم المعالجة</span>
                                {% endif %}
                            </td>
                            <td>{{ doc.user.username }}</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-info doc-action" 
                                            data-action="view" data-id="{{ doc.id }}" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-primary doc-action"
                                            data-action="download" data-id="{{ doc.id }}" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning doc-action"
                                            data-action="edit" data-id="{{ doc.id }}" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger doc-action"
                                            data-action="delete" data-id="{{ doc.id }}" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Document View Modal -->
<div class="modal fade" id="documentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المستند</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Document details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All Checkbox
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('tbody .form-check-input');

    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
    });

    // Filter Form
    const filterForm = document.getElementById('filterForm');
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Handle filter submission
        const formData = new FormData(filterForm);
        // You would typically make an AJAX request here
    });

    // Document Actions
    document.querySelectorAll('.doc-action').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const id = this.dataset.id;
            
            switch(action) {
                case 'view':
                    const modal = new bootstrap.Modal(document.getElementById('documentModal'));
                    modal.show();
                    break;
                case 'download':
                    // Handle document download
                    console.log('Downloading document:', id);
                    break;
                case 'edit':
                    // Handle document edit
                    console.log('Editing document:', id);
                    break;
                case 'delete':
                    if (confirm('هل أنت متأكد من حذف هذا المستند؟')) {
                        // Proceed with deletion
                        console.log('Deleting document:', id);
                    }
                    break;
            }
        });
    });
});
</script>
{% endblock %}