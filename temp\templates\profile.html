{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">الملف الشخصي</h1>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="position-relative d-inline-block">
                            <img src="{{ current_user.avatar|default('https://via.placeholder.com/150') }}" 
                                 class="rounded-circle" alt="صورة الملف الشخصي" width="150" height="150">
                            <button class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle"
                                    style="width: 32px; height: 32px;"
                                    title="تغيير الصورة"
                                    onclick="document.getElementById('avatarInput').click()">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <input type="file" id="avatarInput" class="d-none" accept="image/*">
                    </div>
                    <h5 class="mb-1">{{ current_user.username }}</h5>
                    <p class="text-muted mb-3">{{ current_user.role }}</p>
                    <div class="d-flex justify-content-center mb-3">
                        <div class="px-3 border-end">
                            <h6 class="mb-0">{{ documents_count|default(0) }}</h6>
                            <small class="text-muted">المستندات</small>
                        </div>
                        <div class="px-3">
                            <h6 class="mb-0">{{ activities_count|default(0) }}</h6>
                            <small class="text-muted">النشاطات</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Settings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">الإعدادات</h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="twoFactorAuth">
                            <label class="form-check-label" for="twoFactorAuth">
                                المصادقة الثنائية
                            </label>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Details and Activities -->
        <div class="col-md-8">
            <!-- Profile Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">المعلومات الشخصية</h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" value="{{ current_user.username }}" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" value="{{ current_user.email }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" placeholder="أدخل كلمة المرور الحالية">
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" placeholder="أدخل كلمة المرور الجديدة">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" placeholder="أعد إدخال كلمة المرور الجديدة">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">آخر النشاطات</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for activity in activities|default([]) %}
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                <div class="timeline-icon bg-light rounded-circle p-2">
                                    {% if activity.action == 'upload' %}
                                        <i class="fas fa-upload text-primary"></i>
                                    {% elif activity.action == 'download' %}
                                        <i class="fas fa-download text-success"></i>
                                    {% elif activity.action == 'view' %}
                                        <i class="fas fa-eye text-info"></i>
                                    {% elif activity.action == 'edit' %}
                                        <i class="fas fa-edit text-warning"></i>
                                    {% else %}
                                        <i class="fas fa-file-alt text-secondary"></i>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between">
                                    <h6 class="mb-0">{{ activity.document.title }}</h6>
                                    <small class="text-muted">
                                        {{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                <p class="mb-0 text-muted">{{ activity.action_description }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar Upload
    const avatarInput = document.getElementById('avatarInput');
    
    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Here you would typically upload the file to your server
            const reader = new FileReader();
            reader.onload = function(e) {
                document.querySelector('.rounded-circle').src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // Profile Form
    const profileForm = document.getElementById('profileForm');
    
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Handle profile update
    });

    // Settings Form
    const settingsForm = document.getElementById('settingsForm');
    
    settingsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Handle settings update
    });
});
</script>
{% endblock %}