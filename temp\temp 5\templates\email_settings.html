{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">إعدادات البريد الإلكتروني</h1>
    </div>

    <!-- Email Settings Form -->
    <div class="card">
        <div class="card-body">
            <form method="POST" action="{{ url_for('save_email_settings') }}">
                <div class="mb-3">
                    <label class="form-label" for="mail_server">خادم SMTP</label>
                    <input type="text" class="form-control" id="mail_server" name="mail_server" value="{{ config.MAIL_SERVER }}" required
                           placeholder="مثال: smtp.gmail.com">
                    <div class="form-text">مثال: smtp.gmail.com</div>
                </div>

                <div class="mb-3">
                    <label class="form-label" for="mail_port">منفذ SMTP</label>
                    <input type="number" class="form-control" id="mail_port" name="mail_port" value="{{ config.MAIL_PORT }}" required
                           placeholder="587 أو 465">
                    <div class="form-text">عادةً 587 للـ TLS أو 465 للـ SSL</div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="mail_use_tls" id="mail_use_tls" 
                               {% if config.MAIL_USE_TLS %}checked{% endif %}>
                        <label class="form-check-label" for="mail_use_tls">
                            استخدام TLS
                        </label>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label" for="mail_username">البريد الإلكتروني</label>
                    <input type="email" class="form-control" id="mail_username" name="mail_username" value="{{ config.MAIL_USERNAME }}" required
                           placeholder="<EMAIL>">
                    <div class="form-text">عنوان البريد الإلكتروني المستخدم للإرسال</div>
                </div>

                <div class="mb-3">
                    <label class="form-label" for="mail_password">كلمة المرور</label>
                    <input type="password" class="form-control" id="mail_password" name="mail_password" value="{{ config.MAIL_PASSWORD }}" required
                           placeholder="كلمة مرور البريد أو كلمة مرور التطبيق">
                    <div class="form-text">كلمة المرور الخاصة بالتطبيق (لـ Gmail) أو كلمة مرور البريد الإلكتروني</div>
                </div>

                <button type="button" class="btn btn-secondary me-2" id="testEmail">
                    اختبار الإعدادات
                </button>
                <button type="submit" class="btn btn-primary">
                    حفظ الإعدادات
                </button>
            </form>
        </div>
    </div>

    <!-- Tips Card -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">تعليمات</h5>
        </div>
        <div class="card-body">
            <h6>لاستخدام Gmail:</h6>
            <ol>
                <li>قم بتفعيل المصادقة الثنائية في حساب Gmail</li>
                <li>انتقل إلى إعدادات الأمان > كلمات مرور التطبيقات</li>
                <li>أنشئ كلمة مرور جديدة خاصة بالتطبيق</li>
                <li>استخدم كلمة المرور الجديدة في الإعدادات أعلاه</li>
            </ol>

            <h6>إعدادات عامة:</h6>
            <ul>
                <li>خادم Gmail: smtp.gmail.com</li>
                <li>منفذ Gmail: 587</li>
                <li>يجب تفعيل TLS لـ Gmail</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('testEmail').addEventListener('click', async function() {
    // Disable button and show loading state
    const button = this;
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';

    try {
        const formData = {
            mail_server: document.querySelector('[name="mail_server"]').value,
            mail_port: document.querySelector('[name="mail_port"]').value,
            mail_use_tls: document.querySelector('[name="mail_use_tls"]').checked,
            mail_username: document.querySelector('[name="mail_username"]').value,
            mail_password: document.querySelector('[name="mail_password"]').value
        };

        // Validate required fields
        const requiredFields = ['mail_server', 'mail_port', 'mail_username', 'mail_password'];
        const missingFields = requiredFields.filter(field => !formData[field]);
        if (missingFields.length > 0) {
            throw new Error('يرجى ملء جميع الحقول المطلوبة');
        }

        const response = await fetch("{{ url_for('test_email_settings') }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const data = await response.json();
        
        // Create Bootstrap alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${data.success ? 'success' : 'danger'} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${data.success ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-circle"></i>'}
            ${data.success ? data.message : data.error}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert alert before the form
        const form = document.querySelector('form');
        form.parentNode.insertBefore(alertDiv, form);

        // Auto-dismiss success message after 5 seconds
        if (data.success) {
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

    } catch (error) {
        // Show error in Bootstrap alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            ${error.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        const form = document.querySelector('form');
        form.parentNode.insertBefore(alertDiv, form);
    } finally {
        // Restore button state
        button.disabled = false;
        button.innerHTML = originalText;
    }
});
</script>
{% endblock %}