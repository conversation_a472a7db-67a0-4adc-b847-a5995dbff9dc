document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const documentType = document.getElementById('document_type');

    function enableFields(fields) {
        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.disabled = false;
                if (element.hasAttribute('required')) {
                    element.classList.add('required-field');
                }
            }
        });
    }

    function disableFields(fields) {
        fields.forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.disabled = true;
                element.classList.remove('required-field');
                if (element.tagName === 'SELECT') {
                    element.selectedIndex = 0;
                } else {
                    element.value = '';
                }
            }
        });
    }

    function hideAllSections() {
        ['incomingFields', 'outgoingFields', 'archiveFields'].forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'none';
            }
        });
    }

    // Initialize all form fields
    function initializeFormFields() {
        const allFields = [
            'incoming_date', 'document_date', 'department_id', 'source_department',
            'incoming_document_number', 'outgoing_document_number', 'incoming_subject',
            'outgoing_subject', 'archive_number', 'archive_type', 'archive_subject',
            'outgoing_date', 'destination_department'
        ];
        allFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.required = false;
                element.disabled = true;
                // Don't clear validation state here
                if (element.tagName === 'SELECT') {
                    element.selectedIndex = 0;
                } else {
                    element.value = '';
                }
            }
        });
    }

    // Enable required fields
    function enableRequiredFields(fields) {
        fields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                element.required = true;
                element.disabled = false;
                // Add required-field class for visual feedback
                element.classList.add('required-field');
                // Ensure subject fields have the correct name attribute
                if (field.endsWith('_subject')) {
                    element.setAttribute('name', 'subject');
                }
            }
        });
    }

    // Form validation setup
    function validateForm(e) {
        if (e) e.preventDefault();
        
        let isValid = true;
        const form = uploadForm;
        const type = documentType.value;
        
        // Reset validation state
        form.classList.remove('was-validated');
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });

        // Validate document type
        if (!type) {
            documentType.classList.add('is-invalid');
            return false;
        }

        // Validate required fields based on document type
        const section = document.getElementById(type + 'Fields');
        if (section) {
            // Get all required fields in the current section
            const requiredFields = section.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.disabled && !field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                }
            });
        }

        // Check file upload
        const fileInput = document.getElementById('document_file');
        if (!fileInput.files || !fileInput.files[0]) {
            fileInput.classList.add('is-invalid');
            isValid = false;
        } else if (fileInput.files[0].size > 16 * 1024 * 1024) {
            alert('حجم الملف يتجاوز الحد المسموح به (16 ميجابايت)');
            fileInput.classList.add('is-invalid');
            isValid = false;
        }

        // Apply Bootstrap validation classes
        form.classList.add('was-validated');

        if (isValid) {
            form.submit();
        }
        return false;
    }

    // Add form submit handler
    if (uploadForm) {
        uploadForm.addEventListener('submit', validateForm);
    }

    // Initialize fields on page load
    initializeFormFields();

    // Handle document type change
    documentType.addEventListener('change', function() {
        hideAllSections();
        // Initialize all fields (disable and clear)
        initializeFormFields();
        
        // Show and enable relevant fields based on document type
        const section = document.getElementById(this.value + 'Fields');
        if (section) {
            section.style.display = 'block';
            
            if (this.value === 'incoming') {
                enableRequiredFields([
                    'incoming_date', 'document_date', 'source_department',
                    'incoming_document_number', 'incoming_subject'
                ]);
            } else if (this.value === 'outgoing') {
                enableRequiredFields([
                    'outgoing_date', 'outgoing_document_number',
                    'destination_department', 'outgoing_subject'
                ]);
            } else if (this.value === 'archive') {
                enableRequiredFields(['archive_number', 'archive_type', 'archive_subject']);
            }
            
            // Clear any previous validation state
            section.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
        }
    });

    // File Preview
    const fileInput = document.getElementById('document_file');
    const imagePreview = document.getElementById('image-preview');
    const previewContainer = document.getElementById('preview-container');

    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (!file) return;

        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                document.querySelector('.preview-placeholder').style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
            document.querySelector('.preview-placeholder').style.display = 'block';
            document.querySelector('.preview-placeholder i').className =
                `fas fa-4x text-muted mb-2 ${
                    file.type.includes('pdf') ? 'fa-file-pdf' :
                    file.type.includes('word') ? 'fa-file-word' :
                    'fa-file'
                }`;
        }
    });
});