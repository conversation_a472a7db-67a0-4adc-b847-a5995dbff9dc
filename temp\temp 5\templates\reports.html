{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">التقارير</h1>
    </div>

    <!-- Report Types -->
    <div class="row g-4">
        <!-- Document Statistics Report -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-chart-pie text-primary"></i>
                        إحصائيات المستندات
                    </h5>
                    <p class="card-text">تقرير يوضح إحصائيات المستندات حسب النوع والقسم والفترة الزمنية.</p>
                    <form action="{{ url_for('generate_report', type='statistics') }}" method="POST" class="mt-3">
                        <div class="mb-3">
                            <label class="form-label" for="date_from_stats">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from_stats" name="date_from">
                        </div>
                        <div class="mb-3">
                            <label class="form-label" for="date_to_stats">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to_stats" name="date_to">
                        </div>
                        <div class="d-grid">
                            <button type="submit" name="format" value="excel" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Activity Log Report -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-history text-success"></i>
                        سجل النشاطات
                    </h5>
                    <p class="card-text">تقرير يعرض جميع النشاطات على المستندات (عرض، تحميل، تعديل، حذف).</p>
                    <form action="{{ url_for('generate_report', type='activity') }}" method="POST" class="mt-3">
                        <div class="mb-3">
                            <label class="form-label" for="date_from_activity">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from_activity" name="date_from">
                        </div>
                        <div class="mb-3">
                            <label class="form-label" for="date_to_activity">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to_activity" name="date_to">
                        </div>
                        <div class="d-grid">
                            <button type="submit" name="format" value="excel" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Department Summary Report -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-building text-info"></i>
                        ملخص الأقسام
                    </h5>
                    <p class="card-text">تقرير يوضح ملخص المستندات والنشاطات لكل قسم.</p>
                    <form action="{{ url_for('generate_report', type='department') }}" method="POST" class="mt-3">
                        <div class="mb-3">
                            <label class="form-label" for="department">القسم</label>
                            <select class="form-select" id="department" name="department">
                                <option value="">كل الأقسام</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label" for="date_from_dept">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from_dept" name="date_from">
                        </div>
                        <div class="mb-3">
                            <label class="form-label" for="date_to_dept">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to_dept" name="date_to">
                        </div>
                        <div class="d-grid">
                            <button type="submit" name="format" value="excel" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> تصدير التقرير
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default date ranges to last month
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    // Format dates for input fields
    const formatDate = (date) => date.toISOString().split('T')[0];
    
    // Set default dates for all date inputs
    document.querySelectorAll('input[type="date"]').forEach(input => {
        if (input.id.includes('from')) {
            input.value = formatDate(lastMonth);
        } else {
            input.value = formatDate(today);
        }
    });
});
</script>
{% endblock %}