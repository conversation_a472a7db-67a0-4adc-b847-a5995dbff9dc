"""
Activity model for tracking document operations
"""
from datetime import datetime, UTC
from .database import db

class Activity(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('user.id'), nullable=False)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)  # view, download, edit, delete
    action_description = db.Column(db.String(200))
    timestamp = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(UTC))
    
    # Define relationships with back_populates
    user = db.relationship('User', back_populates='activities')
    document = db.relationship('Document', back_populates='activities')

    def __repr__(self):
        return f'<Activity {self.action} on Document {self.document_id}>'