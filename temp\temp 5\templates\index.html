{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">لوحة التحكم</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('upload') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مستند جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">المستندات الواردة</h6>
                            <h2 class="mb-0 display-6">{{ incoming_count|default(0) }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-inbox fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">المستندات الصادرة</h6>
                            <h2 class="mb-0 display-6">{{ outgoing_count|default(0) }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-paper-plane fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">الأرشفة</h6>
                            <h2 class="mb-0 display-6">{{ archive_count|default(0) }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-archive fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">إجمالي المستندات</h6>
                            <h2 class="mb-0 display-6">{{ total_count|default(0) }}</h2>
                        </div>
                        <div class="icon-circle bg-white bg-opacity-25 rounded-circle p-3">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Recent Documents and Activities -->
    <div class="row">
        <!-- Recent Documents -->
        <div class="col-md-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock text-primary me-2"></i>
                            آخر المستندات
                        </h5>
                        <a href="{{ url_for('documents') }}" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">العنوان</th>
                                    <th class="border-0">النوع</th>
                                    <th class="border-0">التاريخ</th>
                                    <th class="border-0">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_documents|default([]) %}
                                <tr>
                                    <td>{{ doc.title }}</td>
                                    <td>
                                        {% if doc.document_type == 'incoming' %}
                                        <span class="badge bg-primary">وارد</span>
                                        {% elif doc.document_type == 'outgoing' %}
                                        <span class="badge bg-success">صادر</span>
                                        {% else %}
                                        <span class="badge bg-warning">أرشيف</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='view') }}"
                                           class="btn btn-sm btn-outline-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-md-4 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        آخر النشاطات
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="timeline">
                        {% for activity in recent_activities|default([]) %}
                        <div class="d-flex p-3 border-bottom">
                            <div class="flex-shrink-0">
                                <div class="timeline-icon rounded-circle p-2
                                    {% if activity.action == 'upload' %}bg-success
                                    {% elif activity.action == 'view' %}bg-info
                                    {% elif activity.action == 'edit' %}bg-warning
                                    {% else %}bg-primary{% endif %} bg-opacity-25">
                                    <i class="fas {% if activity.action == 'upload' %}fa-upload
                                              {% elif activity.action == 'view' %}fa-eye
                                              {% elif activity.action == 'edit' %}fa-edit
                                              {% else %}fa-file-alt{% endif %} text-dark"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="d-flex justify-content-between">
                                    <h6 class="mb-0 text-dark">{{ activity.action_description }}</h6>
                                    <small class="text-muted">
                                        {{ activity.timestamp.strftime('%H:%M') }}
                                    </small>
                                </div>
                                <small class="text-muted d-block">
                                    {{ activity.timestamp.strftime('%Y-%m-%d') }}
                                </small>
                            </div>
                        </div>
                        {% endfor %}
                        {% if not recent_activities %}
                        <div class="text-center py-4 text-muted">
                            <i class="fas fa-info-circle mb-2"></i>
                            <p class="mb-0">لا توجد نشاطات حديثة</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}