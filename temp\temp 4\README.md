# نظام إدارة الوثائق - مصرف الرشيد

## خطوات تشغيل النظام

1. إنشاء بيئة Python افتراضية:
```bash
python -m venv venv
```

2. تفعيل البيئة الافتراضية:
- Windows:
```bash
venv\Scripts\activate
```
- Linux/Mac:
```bash
source venv/bin/activate
```

3. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

4. تشغيل النظام:
```bash
python app.py
```

5. فتح المتصفح والذهاب إلى:
```
http://localhost:5000/init
```
هذا سينشئ المدير والأقسام في قاعدة البيانات.

6. تسجيل الدخول باستخدام:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## حل المشاكل الشائعة

1. خطأ: "No module named 'flask'"
- تأكد من تفعيل البيئة الافتراضية
- أعد تثبيت المكتبات: `pip install -r requirements.txt`

2. خطأ في قاعدة البيانات:
- احذف الملف `documents.db` إذا وجد
- أعد تشغيل النظام ليتم إنشاء قاعدة بيانات جديدة

3. خطأ في المجلدات:
- تأكد من وجود مجلد `uploads`
- تأكد من الصلاحيات الكافية لإنشاء وتعديل الملفات

## ملاحظات مهمة

- يجب تشغيل `/init` مرة واحدة فقط عند بدء النظام لأول مرة
- تأكد من وجود مجلد `uploads` في نفس مجلد التطبيق
- للمساعدة والدعم الفني، يرجى التواصل مع قسم تقنية المعلومات