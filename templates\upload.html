{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">رفع مستند جديد</h1>
    </div>

    <!-- Upload Form -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form id="uploadForm" action="{{ url_for('upload') }}" enctype="multipart/form-data" method="POST">
                        <!-- Document Type Selection -->
                        <div class="mb-3">
                            <label for="document_type" class="form-label">نوع المستند *</label>
                            <select class="form-select" id="document_type" name="document_type" required>
                                <div class="invalid-feedback">يرجى اختيار نوع المستند</div>
                                <option value="">اختر نوع المستند</option>
                                {% if current_user.role == 'مدير' or current_user.has_permission('وارد') %}
                                <option value="incoming">وارد</option>
                                {% endif %}
                                {% if current_user.role == 'مدير' or current_user.has_permission('صادر') %}
                                <option value="outgoing">صادر</option>
                                {% endif %}
                                {% if current_user.role == 'مدير' or current_user.has_permission('أرشفة') %}
                                <option value="archive">أرشفة</option>
                                {% endif %}
                            </select>
                        </div>


                        <!-- Incoming Document Fields -->
                        <div id="incomingFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="incoming_date" class="form-label">تاريخ الوارد *</label>
                                    <input type="date" class="form-control" id="incoming_date" name="incoming_date" required>
                                    <div class="invalid-feedback">يرجى تحديد التاريخ</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="document_date" class="form-label">تاريخ استلام *</label>
                                    <input type="date" class="form-control" id="document_date" name="document_date" required>
                                    <div class="invalid-feedback">يرجى تحديد التاريخ</div>
                                </div>
                            </div>

                            <div class="mb-3">
                               <label for="department_id" class="form-label">القسم المستلم *</label>
                                {% if current_user.role == 'مدير' %}
                                <select class="form-select" id="department_id" name="department_id">
                                   rem <option value="">اختر القسم</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if dept.id == current_user.department_id %}selected{% endif %}>{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                                {% else %}
                                <input type="hidden" id="department_id" name="department_id" value="{{ current_user.department_id }}">
                                <input type="text" class="form-control" value="{{ current_user.department.name }}" readonly>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="source_department" class="form-label">الجهة الواردة *</label>
                                <input type="text" class="form-control" id="source_department" name="source_department"
                                       list="departments" placeholder="ادخل رمز او اسم القسم" required>
                                <div class="invalid-feedback">يرجى تحديد الجهة</div>
                                <datalist id="departments">
                                    {% for dept in departments %}
                                    <option value="{{ dept.code }} - {{ dept.name }}">
                                    {% endfor %}
                                </datalist>
                            </div>

                            <div class="mb-3">
                                <label for="document_number" class="form-label">رقم الوارد *</label>
                                <input type="text" class="form-control" id="incoming_document_number" name="document_number" required>
                            </div>

                            <div class="mb-3">
                                <label for="incoming_subject" class="form-label">موضوع الكتاب *</label>
                                <textarea class="form-control" id="incoming_subject" name="subject" rows="3" required></textarea>
                                <div class="invalid-feedback">موضوع الكتاب مطلوب</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_attachments" name="has_attachments">
                                    <label class="form-check-label" for="has_attachments">
                                        يوجد مرفقات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Outgoing Document Fields -->
                        <div id="outgoingFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="outgoing_date" class="form-label">تاريخ الصادر *</label>
                                    <input type="date" class="form-control" id="outgoing_date" name="outgoing_date" required>
                                    <div class="invalid-feedback">يرجى تحديد التاريخ</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="document_number" class="form-label">رقم الكتاب *</label>
                                    <input type="text" class="form-control" id="outgoing_document_number" name="document_number" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="destination_department" class="form-label">الجهة المرسل إليها *</label>
                                <input type="text" class="form-control" id="destination_department" name="destination_department"
                                       list="departments" placeholder="ادخل رمز او اسم القسم" required>
                                <div class="invalid-feedback">يرجى تحديد الجهة</div>
                                <datalist id="departments">
                                    {% for dept in departments %}
                                    <option value="{{ dept.code }} - {{ dept.name }}">
                                    {% endfor %}
                                </datalist>
                            </div>

                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    سيتم إرسال المستند تلقائياً إلى البريد الإلكتروني المسجل للقسم المعني
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="outgoing_subject" class="form-label">موضوع الكتاب *</label>
                                <textarea class="form-control" id="outgoing_subject" name="subject" rows="3" required></textarea>
                                <div class="invalid-feedback">موضوع الكتاب مطلوب</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="outgoing_has_attachments" name="has_attachments">
                                    <label class="form-check-label" for="outgoing_has_attachments">
                                        يوجد مرفقات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Archive Document Fields -->
                        <div id="archiveFields" style="display: none;">
                            <div class="mb-3">
                                <label for="archive_number" class="form-label">رقم الأرشفة *</label>
                                <input type="text" class="form-control" id="archive_number" name="archive_number" required>
                                <div class="invalid-feedback">يرجى إدخال رقم الأرشفة</div>
                            </div>
                            <div class="mb-3">
                                <label for="archive_subject" class="form-label">موضوع المستند *</label>
                                <textarea class="form-control" id="archive_subject" name="subject" rows="3" required></textarea>
                                <div class="invalid-feedback">موضوع المستند مطلوب</div>
                            </div>
                            <div class="mb-3">
                                <label for="archive_type" class="form-label">نوع المستند الأرشفة *</label>
                                <input type="text" class="form-control" id="archive_type" name="archive_type"
                                       list="archive_types" placeholder="اختر او اكتب نوع المستند" required>
                                <div class="invalid-feedback">يرجى تحديد نوع المستند</div>
                            </div>
                            <style>
                                .required-field {
                                    border-color: #dc3545;
                                }
                                .required-field:focus {
                                    border-color: #dc3545;
                                    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
                                }
                            </style>
                                <datalist id="archive_types">
                                    <optgroup label="الأنظمة المصرفية">
                                        <option value="جاري عراقي">
                                        <option value="توفير عراقي">
                                        <option value="استاذ عام">
                                        <option value="سفاتج">
                                        <option value="قروض موظفي الدولة">
                                        <option value="TEAM">
                                        <option value="مصارف محلية">
                                        <option value="حوالات">
                                        <option value="جاري دولار">
                                        <option value="قروض السيارات">
                                        <option value="ودائع">
                                        <option value="قروض موظفي المصرف">
                                    </optgroup>
                                    <optgroup label="الخدمات الإلكترونية">
                                        <option value="بنك المعلومات">
                                        <option value="بطاقة ذكية">
                                        <option value="ماستر كارد">
                                        <option value="التوطين">
                                    </optgroup>
                                    <optgroup label="السلف والقروض">
                                        <option value="سلف موظفين">
                                        <option value="سلف زواج">
                                        <option value="سلف متقاعدين">
                                        <option value="سلف متقاعدين 2">
                                        <option value="سلف حوافز الانتاج">
                                        <option value="قروض المواطنين">
                                        <option value="قروض اسكان مجمع الزهور">
                                        <option value="قروض الاسكان بسماية">
                                        <option value="قروض مشاريع صغيرة">
                                    </optgroup>
                                    <optgroup label="الحسابات والتحويلات">
                                        <option value="صندوق التقاعد">
                                        <option value="فروع داخلية دولار">
                                        <option value="بيع دولار بنك مركزي">
                                        <option value="مقاصة رافدين">
                                        <option value="مقاصة رشيد">
                                        <option value="توفير دولار">
                                        <option value="جاري تنمية الدولار">
                                        <option value="جاري تنمية">
                                        <option value="فروع داخلية">
                                        <option value="رواتب المتقاعدين الموقوفة">
                                    </optgroup>
                                    <optgroup label="الأنظمة الإدارية">
                                        <option value="نظام الائتمان 3">
                                        <option value="نظام قسم الائتمان 3">
                                        <option value="المتاخرة عن التسديد">
                                    </optgroup>
                                    <optgroup label="أنواع المستندات">
                                        <option value="اضبارة شخصية">
                                        <option value="عقد">
                                        <option value="وارد">
                                        <option value="صادر">
                                        <option value="قرار">
                                        <option value="تعميم">
                                        <option value="مذكرة">
                                    </optgroup>
                                </datalist>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="mb-3">
                            <label class="form-label">الملف *</label>
                            <div class="mb-2">
                                <input type="file" class="form-control" id="document_file" name="document_file"
                                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required
                                       aria-label="اختر ملف للرفع">
                                <div class="invalid-feedback">يرجى اختيار ملف</div>
                            </div>
                            <div class="form-text">
                                الملفات المسموحة: PDF, Word, Images (الحد الأقصى: 16 ميجابايت)
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <!-- Categories Selection -->
                        <div class="mb-3">
                            <label for="categories" class="form-label">التصنيفات</label>
                            <select class="form-select" id="categories" name="categories" multiple>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.full_path }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">
                                يمكنك اختيار أكثر من تصنيف (اضغط Ctrl للاختيار المتعدد)
                            </div>
                        </div>

                        <!-- Tags -->
                        <div class="mb-3">
                            <label for="tags" class="form-label">الوسوم</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                   placeholder="أدخل الوسوم مفصولة بفواصل">
                            <div class="form-text">
                                مثال: عاجل، مهم، متابعة
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المستند
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview and Guidelines -->
        <div class="col-md-4">
            <!-- File Preview -->
            <div class="card mb-3">
                <div class="card-header">
                    معاينة المستند
                </div>
                <div class="card-body text-center" id="preview-container">
                    <div class="preview-placeholder">
                        <i class="fas fa-file fa-4x text-muted mb-2"></i>
                        <p class="text-muted">سيتم عرض معاينة الملف هنا</p>
                    </div>
                    <img id="image-preview" style="max-width: 100%; display: none;">
                </div>
            </div>

            <!-- Guidelines -->
            <div class="card">
                <div class="card-header">
                    إرشادات
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info"></i>
                            تأكد من ملء جميع الحقول المطلوبة (*)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file text-primary"></i>
                            الحد الأقصى لحجم الملف: 16 ميجابايت
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tags text-warning"></i>
                            استخدم الوسوم لتسهيل البحث عن المستند لاحقاً
                        </li>
                        <li>
                            <i class="fas fa-shield-alt text-success"></i>
                            سيتم تشفير المستند تلقائياً قبل التخزين
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
    .form-label::after {
        content: " *";
        color: #dc3545;
        display: none;
    }
    .required-field + .form-label::after {
        display: inline;
    }
</style>
<script src="{{ url_for('static', filename='js/webcontent.js') }}"></script>
{% endblock %}