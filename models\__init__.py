"""
Models package initialization.
Import order matters due to relationships between models.
"""
# First import db instance
from .database import db

# Then import models in dependency order
from .department import Department
from .user import User
from .category import Category  # Import before Document due to relationship
from .document import Document
from .activity import Activity

# Export all models
__all__ = ['db', 'Department', 'User', 'Category', 'Document', 'Activity']