# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
.env

# Flask
instance/
.webassets-cache

# Database
*.db
*.sqlite3
*.sqlite

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Uploads
uploads/
media/

# System Files
.DS_Store
Thumbs.db
desktop.ini

# Test Coverage
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Documentation
docs/_build/
docs/source/_templates/
docs/source/_static/
docs/source/_themes/

# Sensitive Data
config.py
secrets.json
credentials.json