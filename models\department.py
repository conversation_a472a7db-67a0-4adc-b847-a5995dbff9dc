"""
Department model
"""
from .database import db

class Department(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(120), unique=True, nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True)
    
    # Define relationships
    users = db.relationship('User', back_populates='department', lazy=True)
    documents = db.relationship('Document', back_populates='department', lazy=True)

    def __repr__(self):
        return f'<Department {self.name}>'