{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">إدارة المستخدمين</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i> إضافة مستخدم جديد
            </button>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>الاسم الكامل</th>
                            <th>البريد الإلكتروني</th>
                            <th>القسم</th>
                            <th>الدور</th>
                            <th>الصلاحيات</th>
                            <th>الحالة</th>
                            <th>آخر تسجيل دخول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.username }}</td>
                            <td>{{ user.fullname }}</td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.department.name if user.department else 'غير محدد' }}</td>
                            <td>
                                <span class="badge {% if user.role == 'مدير' %}bg-danger
                                                  {% elif user.role == 'مستخدم' %}bg-primary
                                                  {% else %}bg-secondary{% endif %}">
                                    {{ user.role }}
                                </span>
                            </td>
                            <td>
                                {% if user.permissions %}
                                    {% for perm in user.permissions.split(',') %}
                                        <span class="badge bg-info">{{ perm }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">لا توجد صلاحيات</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if user.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                    {{ 'نشط' if user.is_active else 'غير نشط' }}
                                </span>
                            </td>
                            <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل دخول بعد' }}</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-warning"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editUserModal"
                                            data-user-id="{{ user.id }}"
                                            data-username="{{ user.username }}"
                                            data-fullname="{{ user.fullname }}"
                                            data-email="{{ user.email }}"
                                            data-role="{{ user.role }}"
                                            data-department="{{ user.department_id }}"
                                            data-permissions="{{ user.permissions }}"
                                            data-is-active="{{ 'true' if user.is_active else 'false' }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if current_user.id != user.id %}
                                    <button type="button" class="btn btn-sm btn-danger user-action" 
                                            data-action="delete"
                                            data-user-id="{{ user.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-sm btn-secondary user-action"
                                            data-action="reset-password"
                                            data-user-id="{{ user.id }}">
                                        <i class="fas fa-key"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm" action="{{ url_for('add_user') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم *</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-control" name="fullname" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور *</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">القسم *</label>
                        <select class="form-select" name="department_id" required>
                            <option value="">اختر القسم</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الدور *</label>
                        <select class="form-select" name="role" required>
                            <option value="مشاهد">مشاهد</option>
                            <option value="مستخدم">مستخدم</option>
                            <option value="مدير">مدير</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label d-block">الصلاحيات</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="وارد" id="add_perm_incoming">
                            <label class="form-check-label" for="add_perm_incoming">وارد</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="صادر" id="add_perm_outgoing">
                            <label class="form-check-label" for="add_perm_outgoing">صادر</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="أرشفة" id="add_perm_archive">
                            <label class="form-check-label" for="add_perm_archive">أرشفة</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm" action="{{ url_for('edit_user') }}" method="POST">
                <input type="hidden" name="user_id" id="editUserId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم *</label>
                        <input type="text" class="form-control" name="username" id="editUsername" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الاسم الكامل *</label>
                        <input type="text" class="form-control" name="fullname" id="editFullname" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني *</label>
                        <input type="email" class="form-control" name="email" id="editEmail" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">القسم *</label>
                        <select class="form-select" name="department_id" id="editDepartment" required>
                            <option value="">اختر القسم</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الدور *</label>
                        <select class="form-select" name="role" id="editRole" required>
                            <option value="مشاهد">مشاهد</option>
                            <option value="مستخدم">مستخدم</option>
                            <option value="مدير">مدير</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label d-block">الصلاحيات</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="وارد" id="edit_perm_incoming">
                            <label class="form-check-label" for="edit_perm_incoming">وارد</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="صادر" id="edit_perm_outgoing">
                            <label class="form-check-label" for="edit_perm_outgoing">صادر</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" name="permissions" value="أرشفة" id="edit_perm_archive">
                            <label class="form-check-label" for="edit_perm_archive">أرشفة</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_active" id="editIsActive">
                            <label class="form-check-label">حساب نشط</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Role change handler
    function handleRoleChange(select, permCheckboxes) {
        select.addEventListener('change', function() {
            const isAdmin = this.value === 'مدير';
            permCheckboxes.forEach(checkbox => {
                checkbox.checked = isAdmin;
                checkbox.disabled = isAdmin;
            });
        });
    }

    // Add form role handler
    const addRoleSelect = document.querySelector('#addUserForm [name="role"]');
    const addPermCheckboxes = document.querySelectorAll('#addUserForm [name="permissions"]');
    handleRoleChange(addRoleSelect, addPermCheckboxes);

    // Edit form role handler
    const editRoleSelect = document.querySelector('#editUserForm [name="role"]');
    const editPermCheckboxes = document.querySelectorAll('#editUserForm [name="permissions"]');
    handleRoleChange(editRoleSelect, editPermCheckboxes);

    // Edit User Modal Population
    const editUserModal = document.getElementById('editUserModal');
    if (editUserModal) {
        editUserModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');
            const fullname = button.getAttribute('data-fullname');
            const email = button.getAttribute('data-email');
            const role = button.getAttribute('data-role');
            const department = button.getAttribute('data-department');
            const permissions = (button.getAttribute('data-permissions') || '').split(',');
            const isActive = button.getAttribute('data-is-active') === 'true';
            
            document.getElementById('editUserId').value = userId;
            document.getElementById('editUsername').value = username;
            document.getElementById('editFullname').value = fullname;
            document.getElementById('editEmail').value = email;
            document.getElementById('editRole').value = role;
            document.getElementById('editDepartment').value = department;
            document.getElementById('editIsActive').checked = isActive;

            // Set permissions
            document.getElementById('edit_perm_incoming').checked = permissions.includes('وارد');
            document.getElementById('edit_perm_outgoing').checked = permissions.includes('صادر');
            document.getElementById('edit_perm_archive').checked = permissions.includes('أرشفة');

            // Update permissions state based on role
            const isAdmin = role === 'مدير';
            editPermCheckboxes.forEach(checkbox => {
                checkbox.checked = isAdmin || permissions.includes(checkbox.value);
                checkbox.disabled = isAdmin;
            });
        });
    }

    // Handle user actions
    document.querySelectorAll('.user-action').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const userId = this.dataset.userId;
            
            if (action === 'delete') {
                if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                    window.location.href = `/delete_user/${userId}`;
                }
            } else if (action === 'reset-password') {
                if (confirm('هل أنت متأكد من إعادة تعيين كلمة المرور؟')) {
                    window.location.href = `/reset_password/${userId}`;
                }
            }
        });
    });
});
</script>
{% endblock %}