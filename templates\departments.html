{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">إدارة الأقسام</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                <i class="fas fa-plus"></i> إضافة قسم جديد
            </button>
        </div>
    </div>

    <!-- Departments Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم القسم</th>
                            <th>الرمز</th>
                            <th>البريد الإلكتروني</th>
                            <th>عدد المستندات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dept in departments %}
                        <tr>
                            <td>{{ dept.name }}</td>
                            <td>{{ dept.code }}</td>
                            <td>{{ dept.email or 'لم يتم تحديد البريد' }}</td>
                            <td>{{ dept.documents|length }}</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-warning"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editDepartmentModal"
                                            data-dept-id="{{ dept.id }}"
                                            data-name="{{ dept.name }}"
                                            data-code="{{ dept.code }}"
                                            data-email="{{ dept.email }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger dept-action"
                                            data-action="delete"
                                            data-dept-id="{{ dept.id }}">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addDepartmentForm" action="{{ url_for('add_department') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم القسم</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرمز</label>
                        <input type="text" class="form-control" name="code" required>
                        <div class="form-text">
                            رمز مختصر للقسم، مثال: HR، IT، FIN
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email"
                               placeholder="<EMAIL>">
                        <div class="form-text">
                            سيتم استخدامه للإرسال التلقائي للمستندات الصادرة
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل القسم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editDepartmentForm" action="{{ url_for('edit_department') }}" method="POST">
                <input type="hidden" name="department_id" id="editDeptId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم القسم</label>
                        <input type="text" class="form-control" name="name" id="editName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الرمز</label>
                        <input type="text" class="form-control" name="code" id="editCode" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" name="email" id="editEmail"
                               placeholder="<EMAIL>">
                        <div class="form-text">
                            سيتم استخدامه للإرسال التلقائي للمستندات الصادرة
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit Department Modal Population
    const editDepartmentModal = document.getElementById('editDepartmentModal');
    if (editDepartmentModal) {
        editDepartmentModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const deptId = button.getAttribute('data-dept-id');
            const name = button.getAttribute('data-name');
            const code = button.getAttribute('data-code');
            const email = button.getAttribute('data-email');
            
            document.getElementById('editDeptId').value = deptId;
            document.getElementById('editName').value = name;
            document.getElementById('editCode').value = code;
            document.getElementById('editEmail').value = email || '';
        });
    }

    // Handle department actions
    document.querySelectorAll('.dept-action').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const deptId = this.dataset.deptId;
            
            if (action === 'delete') {
                if (confirm('هل أنت متأكد من حذف هذا القسم؟')) {
                    window.location.href = `/delete_department/${deptId}`;
                }
            }
        });
    });
});
</script>
{% endblock %}