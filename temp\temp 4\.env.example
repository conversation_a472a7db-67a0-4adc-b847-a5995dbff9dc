# Flask Application Settings
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Settings
SQLALCHEMY_DATABASE_URI=sqlite:///documents.db
# For MySQL: mysql://username:password@localhost/dbname
# For PostgreSQL: postgresql://username:password@localhost/dbname

# Email Settings (Gmail Example)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-specific-password

# File Upload Settings
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=********  # 16MB in bytes

# Admin Account Default Settings
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>
ADMIN_FULLNAME=System Administrator

# Logging Settings
LOG_LEVEL=INFO
LOG_FILE=logs/in_out.log

# Optional Settings
# ENABLE_DEBUG_TOOLBAR=True
# ALLOWED_HOSTS=localhost,127.0.0.1
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem