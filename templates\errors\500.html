{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <h1 class="display-1 text-danger">500</h1>
            <h2 class="mb-4">خطأ في الخادم</h2>
            <p class="lead mb-4">عذراً، حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً</p>
            <div class="mt-4">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i> العودة إلى الصفحة الرئيسية
                </a>
            </div>
            
            {% if current_user.is_authenticated and current_user.role == 'مدير' %}
            <div class="mt-4">
                <div class="alert alert-danger">
                    <h4 class="alert-heading">تفاصيل الخطأ (للمدراء فقط):</h4>
                    <hr>
                    <p class="mb-0">{{ error }}</p>
                    {% if error_traceback %}
                    <pre class="mt-3 text-start bg-light p-3 rounded">{{ error_traceback }}</pre>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}