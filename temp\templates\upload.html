{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">رفع مستند جديد</h1>
    </div>

    <!-- Upload Form -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data" method="POST">
                        <!-- Document Type Selection -->
                        <div class="mb-3">
                            <label for="document_type" class="form-label">نوع المستند *</label>
                            <select class="form-select" id="document_type" name="document_type" required>
                                <option value="">اختر نوع المستند</option>
                                {% if current_user.role == 'مدير' or current_user.has_permission('وارد') %}
                                <option value="incoming">وارد</option>
                                {% endif %}
                                {% if current_user.role == 'مدير' or current_user.has_permission('صادر') %}
                                <option value="outgoing">صادر</option>
                                {% endif %}
                                {% if current_user.role == 'مدير' or current_user.has_permission('أرشفة') %}
                                <option value="archive">أرشفة</option>
                                {% endif %}
                            </select>
                        </div>

                        <!-- Basic Document Info -->
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان المستند *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>

                        <!-- Incoming Document Fields -->
                        <div id="incomingFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="incoming_date" class="form-label">تاريخ الوارد *</label>
                                    <input type="date" class="form-control" id="incoming_date" name="incoming_date">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="document_date" class="form-label">تاريخ استلام *</label>
                                    <input type="date" class="form-control" id="document_date" name="document_date">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم المستلم *</label>
                                {% if current_user.role == 'مدير' %}
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">اختر القسم</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if dept.id == current_user.department_id %}selected{% endif %}>{{ dept.name }}</option>
                                    {% endfor %}
                                </select>
                                {% else %}
                                <input type="hidden" id="department_id" name="department_id" value="{{ current_user.department_id }}">
                                <input type="text" class="form-control" value="{{ current_user.department.name }}" readonly>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="source_department" class="form-label">الجهة الواردة *</label>
                                <input type="text" class="form-control" id="source_department" name="source_department" list="departments">
                                <datalist id="departments">
                                    <option value="وزارة الداخلية">
                                    <option value="وزارة المالية">
                                    <option value="وزارة التربية">
                                    <option value="وزارة التعليم العالي">
                                    <option value="وزارة الصحة">
                                </datalist>
                            </div>

                            <div class="mb-3">
                                <label for="document_number" class="form-label">رقم الكتاب *</label>
                                <input type="text" class="form-control" id="document_number" name="document_number">
                            </div>

                            <div class="mb-3">
                                <label for="subject" class="form-label">موضوع الكتاب *</label>
                                <textarea class="form-control" id="subject" name="subject" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="has_attachments" name="has_attachments">
                                    <label class="form-check-label" for="has_attachments">
                                        يوجد مرفقات
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Archive Document Fields -->
                        <div id="archiveFields" style="display: none;">
                            <div class="mb-3">
                                <label for="archive_number" class="form-label">رقم الأرشفة *</label>
                                <input type="text" class="form-control" id="archive_number" name="archive_number">
                            </div>
                            <div class="mb-3">
                                <label for="archive_type" class="form-label">نوع المستند الأرشفة *</label>
                                <select class="form-select" id="archive_type" name="archive_type">
                                    <option value="">اختر نوع المستند</option>
                                    <optgroup label="الأنظمة المصرفية">
                                        <option value="جاري_عراقي">جاري عراقي</option>
                                        <option value="توفير_عراقي">توفير عراقي</option>
                                        <option value="استاذ_عام">استاذ عام</option>
                                        <option value="سفاتج">سفاتج</option>
                                        <option value="قروض_موظفي_الدولة">قروض موظفي الدولة</option>
                                        <option value="team">TEAM</option>
                                        <option value="مصارف_محلية">مصارف محلية</option>
                                        <option value="حوالات">حوالات</option>
                                        <option value="جاري_دولار">جاري دولار</option>
                                        <option value="قروض_السيارات">قروض السيارات</option>
                                        <option value="ودائع">ودائع</option>
                                        <option value="قروض_موظفي_المصرف">قروض موظفي المصرف</option>
                                    </optgroup>
                                    <optgroup label="الخدمات الإلكترونية">
                                        <option value="بنك_المعلومات">بنك المعلومات</option>
                                        <option value="بطاقة_ذكية">بطاقة ذكية</option>
                                        <option value="ماستر_كارد">ماستر كارد</option>
                                        <option value="التوطين">التوطين</option>
                                    </optgroup>
                                    <optgroup label="السلف والقروض">
                                        <option value="سلف_موظفين">سلف موظفين</option>
                                        <option value="سلف_زواج">سلف زواج</option>
                                        <option value="سلف_متقاعدين">سلف متقاعدين</option>
                                        <option value="سلف_متقاعدين_2">سلف متقاعدين 2</option>
                                        <option value="سلف_حوافز_انتاج">سلف حوافز الانتاج</option>
                                        <option value="قروض_المواطنين">قروض المواطنين</option>
                                        <option value="قروض_اسكان_زهور">قروض اسكان مجمع الزهور</option>
                                        <option value="قروض_اسكان_بسماية">قروض الاسكان بسماية</option>
                                        <option value="قروض_مشاريع_صغيرة">قروض مشاريع صغيرة</option>
                                    </optgroup>
                                    <optgroup label="الحسابات والتحويلات">
                                        <option value="صندوق_التقاعد">صندوق التقاعد</option>
                                        <option value="فروع_داخلية_دولار">فروع داخلية دولار</option>
                                        <option value="بيع_دولار_بنك_مركزي">بيع دولار بنك مركزي</option>
                                        <option value="مقاصة_رافدين">مقاصة رافدين</option>
                                        <option value="مقاصة_رشيد">مقاصة رشيد</option>
                                        <option value="توفير_دولار">توفير دولار</option>
                                        <option value="جاري_تنمية_دولار">جاري تنمية الدولار</option>
                                        <option value="جاري_تنمية">جاري تنمية</option>
                                        <option value="فروع_داخلية">فروع داخلية</option>
                                        <option value="رواتب_متقاعدين_موقوفة">رواتب المتقاعدين الموقوفة</option>
                                    </optgroup>
                                    <optgroup label="الأنظمة الإدارية">
                                        <option value="نظام_ائتمان_3">نظام الائتمان 3</option>
                                        <option value="نظام_قسم_ائتمان_3">نظام قسم الائتمان 3</option>
                                        <option value="متاخرة_تسديد">المتاخرة عن التسديد</option>
                                    </optgroup>
                                    <optgroup label="أنواع المستندات">
                                        <option value="اضبارة_شخصية">اضبارة شخصية</option>
                                        <option value="عقد">عقد</option>
                                        <option value="وارد">وارد</option>
                                        <option value="صادر">صادر</option>
                                        <option value="قرار">قرار</option>
                                        <option value="تعميم">تعميم</option>
                                        <option value="مذكرة">مذكرة</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>

                        <!-- File Upload or Scan -->
                        <div class="mb-3">
                            <label class="form-label">الملف *</label>
                            <div class="d-flex gap-2 mb-2">
                                <button type="button" class="btn btn-primary" id="scanButton">
                                    <i class="fas fa-scanner"></i> فتح الماسح الضوئي
                                </button>
                                <div class="flex-grow-1">
                                    <input type="file" class="form-control" id="document_file" name="document_file" 
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                                </div>
                            </div>
                            <div class="form-text">
                                الملفات المسموحة: PDF, Word, Images (الحد الأقصى: 16 ميجابايت)
                            </div>
                            <!-- Scanner Container -->
                            <div id="scannerContainer" class="mt-3" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">خيارات المسح الضوئي</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label class="form-label">الدقة (DPI)</label>
                                                <select class="form-select" id="scannerResolution">
                                                    <option value="100">100 DPI</option>
                                                    <option value="200" selected>200 DPI</option>
                                                    <option value="300">300 DPI</option>
                                                    <option value="600">600 DPI</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">وضع المسح</label>
                                                <select class="form-select" id="scannerPixelType">
                                                    <option value="0">أبيض وأسود</option>
                                                    <option value="1" selected>رمادي</option>
                                                    <option value="2">ملون</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="duplex">
                                                    <label class="form-check-label" for="duplex">
                                                        مسح الوجهين
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoCrop">
                                                    <label class="form-check-label" for="autoCrop">
                                                        قص تلقائي
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-primary" id="startScan">
                                                <i class="fas fa-scanner"></i> بدء المسح
                                            </button>
                                            <button type="button" class="btn btn-secondary" id="rotateScan">
                                                <i class="fas fa-redo"></i> تدوير
                                            </button>
                                            <button type="button" class="btn btn-danger" id="clearScan">
                                                <i class="fas fa-trash"></i> مسح
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id="dwtcontrolContainer" class="mt-3"></div>
                                <div id="scanPreview" class="mt-3 text-center">
                                    <img id="scannedImage" style="max-width: 100%; display: none;">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="tags" class="form-label">الوسوم</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   placeholder="أدخل الوسوم مفصولة بفواصل">
                            <div class="form-text">
                                مثال: عاجل، مهم، متابعة
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المستند
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview and Guidelines -->
        <div class="col-md-4">
            <!-- File Preview -->
            <div class="card mb-3">
                <div class="card-header">
                    معاينة المستند
                </div>
                <div class="card-body text-center" id="preview-container">
                    <div class="preview-placeholder">
                        <i class="fas fa-file fa-4x text-muted mb-2"></i>
                        <p class="text-muted">سيتم عرض معاينة الملف هنا</p>
                    </div>
                    <img id="image-preview" style="max-width: 100%; display: none;">
                </div>
            </div>

            <!-- Guidelines -->
            <div class="card">
                <div class="card-header">
                    إرشادات
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-info-circle text-info"></i>
                            تأكد من ملء جميع الحقول المطلوبة (*)
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-file text-primary"></i>
                            الحد الأقصى لحجم الملف: 16 ميجابايت
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-tags text-warning"></i>
                            استخدم الوسوم لتسهيل البحث عن المستند لاحقاً
                        </li>
                        <li>
                            <i class="fas fa-shield-alt text-success"></i>
                            سيتم تشفير المستند تلقائياً قبل التخزين
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Dynamic Web TWAIN Library -->
<script src="https://unpkg.com/dwt/dist/dynamsoft.webtwain.min.js"></script>
<script>
// Initialize scanner variables
Dynamsoft.DWT.ResourcesPath = "https://unpkg.com/dwt/dist";
Dynamsoft.DWT.ProductKey = 'DLS2eyJoYW5kc2hha2VDb2RlIjoiMjAwMDAxLTE2NDk4Mjk3OTI2MzUiLCJvcmdhbml6YXRpb25JRCI6IjIwMDAwMSJ9';

let DWObject;

document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('document_file');
    const imagePreview = document.getElementById('image-preview');
    const previewContainer = document.getElementById('preview-container');
    const documentType = document.getElementById('document_type');
    const incomingFields = document.getElementById('incomingFields');
    const scannerContainer = document.getElementById('scannerContainer');
    const scannedImage = document.getElementById('scannedImage');

    // Initialize scanner
    Dynamsoft.DWT.Load().then(function() {
        DWObject = Dynamsoft.DWT.GetWebTwain('dwtcontrolContainer');
    }).catch(function(err) {
        console.error(err);
    });

    // Scanner toggle button
    document.getElementById('scanButton').addEventListener('click', function() {
        scannerContainer.style.display = scannerContainer.style.display === 'none' ? 'block' : 'none';
    });

    // Start scan button handler
    document.getElementById('startScan').addEventListener('click', function() {
        if (DWObject) {
            const resolution = parseInt(document.getElementById('scannerResolution').value);
            const pixelType = parseInt(document.getElementById('scannerPixelType').value);
            const duplex = document.getElementById('duplex').checked;
            const autoCrop = document.getElementById('autoCrop').checked;

            // Configure scanner settings
            DWObject.SelectSource().then(function() {
                DWObject.Resolution = resolution;
                DWObject.PixelType = pixelType;
                if (duplex) {
                    DWObject.IfDuplexEnabled = true;
                    DWObject.IfFeederEnabled = true;
                }
                
                return DWObject.AcquireImage();
            }).then(function() {
                if (autoCrop) {
                    return DWObject.CropAll();
                }
                return Promise.resolve();
            }).then(function() {
                // Show preview
                DWObject.ConvertToBase64([0], Dynamsoft.DWT.EnumDWT_ImageType.IT_JPG).then(function(base64) {
                    scannedImage.src = 'data:image/jpeg;base64,' + base64;
                    scannedImage.style.display = 'block';
                });
                
                // Convert to PDF
                return DWObject.ConvertToBlob([0], 'PDF');
            }).then(function(blob) {
                const file = new File([blob], 'scanned_document.pdf', { type: 'application/pdf' });
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                fileInput.files = dataTransfer.files;
                
                // Update main preview
                previewContainer.querySelector('.preview-placeholder i').className = 'fas fa-file-pdf fa-4x text-muted mb-2';
                imagePreview.style.display = 'none';
                document.querySelector('.preview-placeholder').style.display = 'block';
            }).catch(function(err) {
                console.error(err);
                alert('حدث خطأ أثناء المسح الضوئي. يرجى المحاولة مرة أخرى.');
            });
        }
    });

    // Rotate scan button handler
    document.getElementById('rotateScan').addEventListener('click', function() {
        if (DWObject && DWObject.HowManyImagesInBuffer > 0) {
            DWObject.RotateRight(0).then(function() {
                // Update preview
                DWObject.ConvertToBase64([0], Dynamsoft.DWT.EnumDWT_ImageType.IT_JPG).then(function(base64) {
                    scannedImage.src = 'data:image/jpeg;base64,' + base64;
                });
            });
        }
    });

    // Clear scan button handler
    document.getElementById('clearScan').addEventListener('click', function() {
        if (DWObject) {
            DWObject.RemoveAllImages();
            fileInput.value = '';
            scannedImage.style.display = 'none';
            imagePreview.style.display = 'none';
            document.querySelector('.preview-placeholder').style.display = 'block';
            document.querySelector('.preview-placeholder i').className = 'fas fa-file fa-4x text-muted mb-2';
        }
    });

    // Show/Hide document fields based on document type
    documentType.addEventListener('change', function() {
        const archiveFields = document.getElementById('archiveFields');
        
        // Hide all fields first
        incomingFields.style.display = 'none';
        archiveFields.style.display = 'none';
        
        // Reset all required fields
        ['incoming_date', 'document_date', 'department_id', 'source_department',
         'document_number', 'subject', 'archive_number', 'archive_type'].forEach(field => {
            const element = document.getElementById(field);
            if (element) element.required = false;
        });
        
        // Show and set required fields based on document type
        if (this.value === 'incoming') {
            incomingFields.style.display = 'block';
            ['incoming_date', 'document_date', 'department_id', 'source_department',
             'document_number', 'subject'].forEach(field => {
                const element = document.getElementById(field);
                if (element) element.required = true;
            });
        } else if (this.value === 'archive') {
            archiveFields.style.display = 'block';
            ['archive_number', 'archive_type'].forEach(field => {
                const element = document.getElementById(field);
                if (element) element.required = true;
            });
        }
    });

    // File Preview
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (!file) return;

        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                document.querySelector('.preview-placeholder').style.display = 'none';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
            document.querySelector('.preview-placeholder').style.display = 'block';
            document.querySelector('.preview-placeholder i').className =
                `fas fa-4x text-muted mb-2 ${
                    file.type.includes('pdf') ? 'fa-file-pdf' :
                    file.type.includes('word') ? 'fa-file-word' :
                    'fa-file'
                }`;
        }
    });

    // Form Submission
    uploadForm.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return;
        }
        
        // Validate file size
        const file = fileInput.files[0];
        if (file && file.size > 16 * 1024 * 1024) {
            alert('حجم الملف يتجاوز الحد المسموح به (16 ميجابايت)');
            e.preventDefault();
            return;
        }
    });

    function validateForm() {
        let isValid = true;
        
        if (documentType.value === 'incoming' || documentType.value === 'archive') {
            let requiredFields = [];
            
            if (documentType.value === 'incoming') {
                requiredFields = ['incoming_date', 'document_date',
                                'source_department', 'document_number', 'subject'];
                // Handle department_id separately
                const deptField = document.getElementById('department_id');
                if (!deptField.value) {
                    if (deptField.tagName === 'SELECT') {
                        deptField.classList.add('is-invalid');
                        isValid = false;
                    }
                } else {
                    if (deptField.tagName === 'SELECT') {
                        deptField.classList.remove('is-invalid');
                    }
                }
            } else if (documentType.value === 'archive') {
                requiredFields = ['archive_number', 'archive_type'];
            }
            
            for (const fieldId of requiredFields) {
                const field = document.getElementById(fieldId);
                if (!field.value) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            }
        }
        
        return isValid;
    }
});
</script>
{% endblock %}