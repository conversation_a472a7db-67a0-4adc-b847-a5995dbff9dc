"""
Category model for document classification
"""
from .database import db, document_categories

class Category(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.<PERSON>umn(db.Text)
    # Self-referential relationship for hierarchical categories
    parent_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    parent = db.relationship('Category', backref=db.backref('children', cascade='all, delete-orphan'), remote_side=[id])
    
    # Relationship with documents through association table
    documents = db.relationship('Document', secondary=document_categories,
                              lazy='dynamic')
    
    created_at = db.Column(db.DateTime, nullable=False, default=db.func.current_timestamp())
    
    def __repr__(self):
        return f'<Category {self.name}>'
    
    @property
    def full_path(self):
        """Get the full hierarchical path of the category"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name