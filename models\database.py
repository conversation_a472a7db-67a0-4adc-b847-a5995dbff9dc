"""
Database configuration and utilities
"""
from flask_sqlalchemy import SQLAlchemy

# Create database instance
db = SQLAlchemy()

# Association table for document categories (many-to-many)
document_categories = db.<PERSON>('document_categories',
    db.<PERSON>umn('document_id', db.<PERSON><PERSON>, db.<PERSON>('document.id'), primary_key=True),
    db.<PERSON>n('category_id', db.In<PERSON>ger, db.<PERSON>('category.id'), primary_key=True)
)