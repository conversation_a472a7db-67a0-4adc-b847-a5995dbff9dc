{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">بحث متقدم</h1>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <!-- Text Search -->
                <div class="col-12">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchQuery" name="query" 
                               placeholder="ابحث في عنوان المستند، الوصف، المحتوى...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="col-md-3">
                    <label class="form-label" for="document_type">نوع المستند</label>
                    <select class="form-select" id="document_type" name="document_type" aria-label="نوع المستند">
                        <option value="">الكل</option>
                        <option value="incoming">وارد</option>
                        <option value="outgoing">صادر</option>
                        <option value="archive">ارشفة</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label" for="user">المسؤول</label>
                    <select class="form-select" id="user" name="user" aria-label="المسؤول">
                        <option value="">الكل</option>
                        {% for user in users|default([]) %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Date Range -->
                <div class="col-md-3">
                    <label class="form-label" for="date_from">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" aria-label="من تاريخ">
                </div>

                <div class="col-md-3">
                    <label class="form-label" for="date_to">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" aria-label="إلى تاريخ">
                </div>

                <div class="col-md-6">
                    <label class="form-label" for="tags">الوسوم</label>
                    <input type="text" class="form-control" id="tags" name="tags"
                           placeholder="أدخل الوسوم مفصولة بفواصل" aria-label="الوسوم">
                </div>

                <!-- Additional Filters -->
                <div class="col-12">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="searchContent" name="search_content">
                        <label class="form-check-label" for="searchContent">
                            البحث في محتوى المستندات
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="caseSensitive" name="case_sensitive">
                        <label class="form-check-label" for="caseSensitive">
                            مطابقة حالة الأحرف
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">نتائج البحث</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary print-btn" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="searchResults">
                {% if results|default(False) %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>رقم الكتاب</th>
                                <th>رقم الأرشفة</th>
                                <th>نوع الأرشفة</th>
                                <th>تاريخ الاستلام</th>
                                <th>الجهة</th>
                                <th>المسؤول</th>
                                <th class="actions-column">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in results %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        {{ doc.title }}
                                        {% if doc.highlight %}
                                        <small class="text-muted ms-2">(تطابق: {{ doc.highlight }})</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                    <span class="badge bg-primary">وارد</span>
                                    {% elif doc.document_type == 'outgoing' %}
                                    <span class="badge bg-success">صادر</span>
                                    {% else %}
                                    <span class="badge bg-info">ارشفة</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>{{ doc.document_number or '-' }}</td>
                                <td>{{ doc.archive_number or '-' if doc.document_type == 'archive' else '-' }}</td>
                                <td>{{ doc.archive_type or '-' if doc.document_type == 'archive' else '-' }}</td>
                                <td>{{ doc.incoming_date.strftime('%Y-%m-%d') if doc.document_type == 'incoming' and doc.incoming_date else '-' }}</td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                        {{ doc.source_department or '-' }}
                                    {% elif doc.document_type == 'outgoing' %}
                                        {{ doc.destination_department or '-' }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ doc.user.username }}</td>
                                <td class="actions-column">
                                    <div class="btn-group">
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='view') }}"
                                           class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='download') }}"
                                           class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% if current_user.role == 'مدير' or doc.user_id == current_user.id %}
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='edit') }}"
                                           class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='delete') }}"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');"
                                           class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب تعديل معايير البحث</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
@media print {
    /* إخفاء العناصر غير المطلوبة للطباعة */
    .no-print,
    #searchForm,
    .btn-group,
    .form-check,
    .actions-column {
        display: none !important;
    }

    /* تنسيق العنوان */
    h1.h2 {
        text-align: center;
        margin-bottom: 20px;
    }
    
    /* تنسيق الجدول */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
        page-break-inside: auto;
    }
    
    .table thead {
        display: table-header-group;
    }
    
    .table tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }
    
    .table th,
    .table td {
        padding: 8px;
        border: 1px solid #000;
        text-align: right;
    }
    
    .table thead th {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    /* تنسيق البطاقات */
    .badge {
        border: 1px solid #000;
        padding: 0.25em 0.5em;
        color: #000 !important;
        background-color: transparent !important;
    }
    
    /* تنسيق الحاوية */
    .container-fluid {
        width: 100%;
        padding: 0;
        margin: 0;
    }

    /* إزالة الظلال والتأثيرات */
    .card {
        border: none;
        box-shadow: none;
    }

    .card-header {
        border-bottom: 2px solid #000;
        margin-bottom: 1rem;
    }

    /* تحسين مظهر النص */
    body {
        font-size: 12pt;
        line-height: 1.3;
    }

    /* إضافة ترقيم الصفحات */
    @page {
        margin: 1cm;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Helper Functions
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    document.querySelector('#searchResults').insertAdjacentElement('beforebegin', alertDiv);
    setTimeout(() => alertDiv.remove(), 3000);
}

function initTooltips() {
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(el => {
            const tooltip = bootstrap.Tooltip.getInstance(el);
            if (tooltip) tooltip.dispose();
            new bootstrap.Tooltip(el);
        });
    }
}

function renderSearchResults(results) {
    if (results.length === 0) {
        return `
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد نتائج</h5>
                <p class="text-muted">جرب تعديل معايير البحث</p>
            </div>
        `;
    }

    return `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>التاريخ</th>
                        <th>رقم الكتاب</th>
                        <th>رقم الأرشفة</th>
                        <th>نوع الأرشفة</th>
                        <th>تاريخ الاستلام</th>
                        <th>الجهة</th>
                        <th>المسؤول</th>
                        <th class="actions-column">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    ${results.map(doc => {
                        const sourceOrDestination = doc.document_type === 'incoming' 
                            ? doc.source_department || '-'
                            : doc.document_type === 'outgoing'
                                ? doc.destination_department || '-'
                                : '-';

                        const documentTypeBadge = doc.document_type === 'incoming'
                            ? '<span class="badge bg-primary">وارد</span>'
                            : doc.document_type === 'outgoing'
                                ? '<span class="badge bg-success">صادر</span>'
                                : '<span class="badge bg-info">ارشفة</span>';

                        return `
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        ${doc.title}
                                    </div>
                                </td>
                                <td>${documentTypeBadge}</td>
                                <td>${doc.created_at}</td>
                                <td>${doc.document_number || '-'}</td>
                                <td>${doc.document_type === 'archive' ? (doc.archive_number || '-') : '-'}</td>
                                <td>${doc.document_type === 'archive' ? (doc.archive_type || '-') : '-'}</td>
                                <td>${doc.document_type === 'incoming' && doc.incoming_date ? doc.incoming_date : '-'}</td>
                                <td>${sourceOrDestination}</td>
                                <td>${doc.user}</td>
                                <td class="actions-column">
                                    <div class="btn-group">
                                        <a href="/document/${doc.id}/view" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/document/${doc.id}/download" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        ${doc.is_owner || doc.is_admin ? `
                                            <a href="/document/${doc.id}/edit" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/document/${doc.id}/delete" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');"
                                               class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        ` : ''}
                                    </div>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function showLoadingSpinner() {
    document.getElementById('searchResults').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري البحث...</span>
            </div>
            <p class="mt-2">جاري البحث...</p>
        </div>
    `;
}

async function performSearch(params) {
    try {
        showLoadingSpinner();
        const response = await fetch(`/search?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });
        const results = await response.json();
        document.getElementById('searchResults').innerHTML = renderSearchResults(results);
        initTooltips();
    } catch (error) {
        console.error('Error:', error);
        showAlert('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.', 'danger');
    }
}

async function exportToExcel() {
    const exportBtn = document.querySelector('button[title="تصدير إلى Excel"]');
    if (!exportBtn) return;

    const originalContent = exportBtn.innerHTML;
    exportBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
    exportBtn.disabled = true;

    try {
        const params = new URLSearchParams(window.location.search);
        const response = await fetch(`/search/download?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });

        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.message || 'حدث خطأ أثناء تصدير النتائج');
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        const timestamp = new Date().toISOString().slice(0,19).replace(/[-:]/g, '');
        a.href = url;
        a.download = `نتائج_البحث_${timestamp}.xlsx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        showAlert('تم تصدير النتائج بنجاح');
    } catch (error) {
        console.error('Error:', error);
        showAlert(error.message, 'danger');
    } finally {
        exportBtn.innerHTML = originalContent;
        exportBtn.disabled = false;
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    window.searchRuntime = window.searchRuntime || {
        initialized: true
    };

    // Initialize form handling
    const searchForm = document.getElementById('searchForm');
    const printBtn = document.querySelector('.print-btn');

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            performSearch(new URLSearchParams(formData));
        });
    }

    if (printBtn) {
        printBtn.addEventListener('click', () => window.print());
    }

    // Make sure Bootstrap is loaded before initializing tooltips
    if (typeof bootstrap !== 'undefined') {
        initTooltips();
    } else {
        console.warn('Bootstrap is not loaded. Tooltips will not be initialized.');
    }
});
</script>
{% endblock %}