{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">تعديل المستند</h1>
    </div>

    <!-- Edit Form -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form id="editForm" action="{{ url_for('update_document', id=document.id) }}" method="POST" enctype="multipart/form-data">
                        <!-- Basic Document Info -->
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان المستند *</label>
                            <input type="text" class="form-control" id="title" name="title" value="{{ document.title }}" required>
                        </div>

                        {% if document.document_type == 'incoming' %}
                        <!-- Incoming Document Fields -->
                        <div id="incomingFields">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="incoming_date" class="form-label">تاريخ الوارد *</label>
                                    <input type="date" class="form-control" id="incoming_date" name="incoming_date" 
                                           value="{{ document.incoming_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="document_date" class="form-label">تاريخ استلام *</label>
                                    <input type="date" class="form-control" id="document_date" name="document_date"
                                           value="{{ document.document_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="department_id" class="form-label">القسم المستلم *</label>
                                {% if current_user.role == 'مدير' %}
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if dept.id == document.department_id %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                {% else %}
                                <input type="hidden" id="department_id" name="department_id" value="{{ document.department_id }}">
                                <input type="text" class="form-control" value="{{ document.department.name }}" readonly
                                       id="department_name" title="القسم المستلم" aria-label="القسم المستلم">
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="source_department" class="form-label">الجهة الواردة *</label>
                                <input type="text" class="form-control" id="source_department" name="source_department"
                                       value="{{ document.source_department }}" required list="departments">
                                <datalist id="departments">
                                    <option value="وزارة الداخلية">
                                    <option value="وزارة المالية">
                                    <option value="وزارة التربية">
                                    <option value="وزارة التعليم العالي">
                                    <option value="وزارة الصحة">
                                </datalist>
                            </div>

                            <div class="mb-3">
                                <label for="document_number" class="form-label">رقم الكتاب *</label>
                                <input type="text" class="form-control" id="document_number" name="document_number"
                                       value="{{ document.document_number }}" required>
                            </div>

                        {% elif document.document_type == 'outgoing' %}
                        <!-- Outgoing Document Fields -->
                        <div id="outgoingFields">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="document_date" class="form-label">تاريخ الصادر *</label>
                                    <input type="date" class="form-control" id="document_date" name="document_date"
                                           value="{{ document.document_date.strftime('%Y-%m-%d') }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="document_number" class="form-label">رقم الكتاب *</label>
                                    <input type="text" class="form-control" id="document_number" name="document_number"
                                           value="{{ document.document_number }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="destination_department" class="form-label">الجهة المرسل إليها *</label>
                                <input type="text" class="form-control" id="destination_department" name="destination_department"
                                       value="{{ document.destination_department }}" required list="departments">
                                <datalist id="departments">
                                    <option value="وزارة الداخلية">
                                    <option value="وزارة المالية">
                                    <option value="وزارة التربية">
                                    <option value="وزارة التعليم العالي">
                                    <option value="وزارة الصحة">
                                </datalist>
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="subject" class="form-label">موضوع الكتاب *</label>
                            <textarea class="form-control" id="subject" name="subject" rows="3" required>{{ document.subject }}</textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="has_attachments" name="has_attachments"
                                       {% if document.has_attachments %}checked{% endif %}>
                                <label class="form-check-label" for="has_attachments">
                                    يوجد مرفقات
                                </label>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="mb-3">
                            <label class="form-label">الملف</label>
                            <div class="d-flex gap-2 mb-2">
                                <div class="flex-grow-1">
                                    <input type="file" class="form-control" id="document_file" name="document_file"
                                           accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                           title="ملف المستند" aria-label="ملف المستند">
                                </div>
                            </div>
                            <div class="form-text">
                                الملفات المسموحة: PDF, Word, Images (الحد الأقصى: 16 ميجابايت)
                                <br>
                                اترك هذا الحقل فارغاً إذا لم ترغب في تغيير الملف
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ document.description }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="tags" class="form-label">الوسوم</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   value="{{ document.tags }}" placeholder="أدخل الوسوم مفصولة بفواصل">
                            <div class="form-text">
                                مثال: عاجل، مهم، متابعة
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-times"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Current File Preview -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    الملف الحالي
                </div>
                <div class="card-body text-center">
                    <p>
                        <i class="fas fa-file fa-3x mb-3"></i>
                        <br>
                        {{ document.file_path.split('/')[-1] }}
                    </p>
                    <a href="{{ url_for('download_document', id=document.id) }}" class="btn btn-primary">
                        <i class="fas fa-download"></i> تحميل الملف
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}