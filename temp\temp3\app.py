from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, abort, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, UTC
from flask_mail import Mail, Message
import os
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///documents.db'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['ALLOWED_EXTENSIONS'] = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}

# تكوين إعدادات البريد الإلكتروني
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = os.environ.get('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.environ.get('MAIL_PASSWORD')

db = SQLAlchemy(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'
mail = Mail(app)
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# Ensure upload directory exists
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    fullname = db.Column(db.String(150), nullable=False)  # Added full name field
    password = db.Column(db.String(120), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False, default='مستخدم')  # مدير، مستخدم، مشاهد
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    permissions = db.Column(db.String(200))  # Comma-separated permissions: صادر,وارد,أرشفة
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(UTC))
    last_login = db.Column(db.DateTime)
    documents = db.relationship('Document', backref='user', lazy=True)
    activities = db.relationship('Activity', backref='user', lazy=True)

    def has_permission(self, permission):
        if self.role == 'مدير':
            return True
        if not self.permissions:
            return False
        return permission in self.permissions.split(',')

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(50), unique=True)
    documents = db.relationship('Document', backref='department', lazy=True)
    users = db.relationship('User', backref='department', lazy=True)

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    file_path = db.Column(db.String(255), nullable=False)
    document_type = db.Column(db.String(50))  # 'incoming', 'outgoing', or 'archive'
    category = db.Column(db.String(50))
    
    # Archive specific fields
    archive_number = db.Column(db.String(100))  # رقم الأرشفة
    archive_type = db.Column(db.String(50))     # نوع المستند الأرشفة
    
    # Fields for both incoming and outgoing documents
    document_number = db.Column(db.String(100))  # رقم الكتاب
    document_date = db.Column(db.Date)  # تاريخ الكتاب
    subject = db.Column(db.Text)  # الموضوع
    has_attachments = db.Column(db.Boolean, default=False)  # هل توجد مرفقات
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))  # ربط مع الأقسام
    
    # Incoming document specific fields
    incoming_date = db.Column(db.Date)  # تاريخ الوارد
    source_department = db.Column(db.String(200))  # الجهة الواردة
    
    # Outgoing document specific fields
    destination_department = db.Column(db.String(200))  # الجهة المرسل إليها
    
    reference_number = db.Column(db.String(50))
    tags = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(UTC))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(UTC), onupdate=lambda: datetime.now(UTC))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.String(50), default='pending')
    activities = db.relationship('Activity', backref='document', lazy=True)

class Activity(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    document_id = db.Column(db.Integer, db.ForeignKey('document.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)
    action_description = db.Column(db.String(255))
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(UTC))
    notes = db.Column(db.Text)

def send_email_notification(recipients, subject, body):
    """إرسال إشعار بالبريد الإلكتروني"""
    try:
        msg = Message(subject,
                     sender=app.config['MAIL_USERNAME'],
                     recipients=recipients)
        msg.body = body
        mail.send(msg)
        return True
    except Exception as e:
        print(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
        return False

def create_activity(user_id, document_id, action, description=None):
    """إنشاء نشاط جديد وإرسال إشعارات"""
    activity = Activity(
        user_id=user_id,
        document_id=document_id,
        action=action,
        action_description=description
    )
    db.session.add(activity)
    db.session.commit()
    
    # إرسال إشعارات للمستخدمين في نفس القسم
    try:
        document = Document.query.get(document_id)
        if document:
            department_users = User.query.filter_by(
                department_id=document.department_id,
                is_active=True
            ).all()
            
            recipients = [user.email for user in department_users if user.id != user_id]
            if recipients:
                subject = f"نشاط جديد على المستند: {document.title}"
                actor = User.query.get(user_id)
                body = f"""
تم {action} على المستند: {document.title}
بواسطة: {actor.fullname}
القسم: {document.department.name if document.department else 'غير محدد'}
الوصف: {description if description else 'لا يوجد وصف'}
                """
                send_email_notification(recipients, subject, body)
    except Exception as e:
        print(f"خطأ في إرسال الإشعارات: {str(e)}")

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# Routes
@app.route('/')
@login_required
def index():
    # Only show documents from user's department unless they're admin
    query = Document.query
    if current_user.role != 'مدير':
        query = query.filter_by(department_id=current_user.department_id)
    
    incoming_count = query.filter_by(document_type='incoming').count()
    outgoing_count = query.filter_by(document_type='outgoing').count()
    pending_count = query.filter_by(status='pending').count()
    completed_count = query.filter_by(status='completed').count()
    
    recent_documents = query.order_by(Document.created_at.desc()).limit(5).all()
    recent_activities = Activity.query.filter(
        Activity.document_id.in_([doc.id for doc in recent_documents])
    ).order_by(Activity.timestamp.desc()).limit(10).all()
    
    return render_template('index.html',
                         incoming_count=incoming_count,
                         outgoing_count=outgoing_count,
                         pending_count=pending_count,
                         completed_count=completed_count,
                         recent_documents=recent_documents,
                         recent_activities=recent_activities)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = bool(request.form.get('remember'))
        
        user = User.query.filter_by(username=username).first()
        if user and check_password_hash(user.password, password):
            if not user.is_active:
                flash('حسابك غير نشط. يرجى التواصل مع المدير', 'danger')
                return render_template('login.html')
            
            login_user(user, remember=remember)
            user.last_login = datetime.now(UTC)
            db.session.commit()
            
            return redirect(url_for('index'))
        
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/users')
@login_required
def users():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('index'))
    users = User.query.all()
    departments = Department.query.all()
    return render_template('users.html', users=users, departments=departments)

@app.route('/add_user', methods=['POST'])
@login_required
def add_user():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بإضافة مستخدمين', 'danger')
        return redirect(url_for('users'))
    username = request.form.get('username')
    fullname = request.form.get('fullname')  # Get full name from form
    email = request.form.get('email')
    password = request.form.get('password')
    role = request.form.get('role')
    department_id = request.form.get('department_id')
    permissions = ','.join(request.form.getlist('permissions'))
    
    
    if User.query.filter_by(username=username).first():
        flash('اسم المستخدم موجود مسبقاً', 'danger')
        return redirect(url_for('users'))
    
    if User.query.filter_by(email=email).first():
        flash('البريد الإلكتروني موجود مسبقاً', 'danger')
        return redirect(url_for('users'))
    
    user = User(
        username=username,
        fullname=fullname,  # Add full name
        email=email,
        password=generate_password_hash(password),
        role=role,
        department_id=department_id,
        permissions=permissions
    )
    db.session.add(user)
    db.session.commit()
    
    flash('تم إضافة المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/edit_user', methods=['POST'])
@login_required
def edit_user():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بتعديل المستخدمين', 'danger')
        return redirect(url_for('users'))
    
    user_id = request.form.get('user_id')
    username = request.form.get('username')
    fullname = request.form.get('fullname')
    email = request.form.get('email')
    role = request.form.get('role')
    department_id = request.form.get('department_id')
    permissions = ','.join(request.form.getlist('permissions'))
    is_active = 'is_active' in request.form
    
    user = User.query.get_or_404(user_id)
    
    # Check if username is taken by another user
    existing_user = User.query.filter_by(username=username).first()
    if existing_user and existing_user.id != user.id:
        flash('اسم المستخدم موجود مسبقاً', 'danger')
        return redirect(url_for('users'))
    
    # Check if email is taken by another user
    existing_user = User.query.filter_by(email=email).first()
    if existing_user and existing_user.id != user.id:
        flash('البريد الإلكتروني موجود مسبقاً', 'danger')
        return redirect(url_for('users'))
    
    user.username = username
    user.fullname = fullname
    user.email = email
    user.role = role
    user.department_id = department_id
    user.permissions = permissions
    user.is_active = is_active
    db.session.commit()
    
    flash('تم تحديث بيانات المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/delete_user/<int:id>')
@login_required
def delete_user(id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بحذف المستخدمين', 'danger')
        return redirect(url_for('users'))
    
    user = User.query.get_or_404(id)
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'danger')
        return redirect(url_for('users'))
    
    db.session.delete(user)
    db.session.commit()
    
    flash('تم حذف المستخدم بنجاح', 'success')
    return redirect(url_for('users'))

@app.route('/reset_password/<int:id>')
@login_required
def reset_password(id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بإعادة تعيين كلمات المرور', 'danger')
        return redirect(url_for('users'))
    
    user = User.query.get_or_404(id)
    new_password = 'Password123!'  # Default password
    user.password = generate_password_hash(new_password)
    db.session.commit()
    
    flash(f'تم إعادة تعيين كلمة المرور للمستخدم {user.username}. كلمة المرور الجديدة هي: {new_password}', 'success')
    return redirect(url_for('users'))

@app.route('/departments')
@login_required
def departments():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بالوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('index'))
    departments = Department.query.all()
    return render_template('departments.html', departments=departments)

@app.route('/add_department', methods=['POST'])
@login_required
def add_department():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بإضافة أقسام', 'danger')
        return redirect(url_for('departments'))
    
    name = request.form.get('name')
    code = request.form.get('code')
    
    if Department.query.filter_by(code=code).first():
        flash('رمز القسم موجود مسبقاً', 'danger')
        return redirect(url_for('departments'))
    
    department = Department(name=name, code=code)
    db.session.add(department)
    db.session.commit()
    
    flash('تم إضافة القسم بنجاح', 'success')
    return redirect(url_for('departments'))

@app.route('/edit_department', methods=['POST'])
@login_required
def edit_department():
    if current_user.role != 'مدير':
        flash('غير مصرح لك بتعديل الأقسام', 'danger')
        return redirect(url_for('departments'))
    
    department_id = request.form.get('department_id')
    name = request.form.get('name')
    code = request.form.get('code')
    
    department = Department.query.get_or_404(department_id)
    
    # Check if code is taken by another department
    existing_dept = Department.query.filter_by(code=code).first()
    if existing_dept and existing_dept.id != department.id:
        flash('رمز القسم موجود مسبقاً', 'danger')
        return redirect(url_for('departments'))
    
    department.name = name
    department.code = code
    db.session.commit()
    
    flash('تم تحديث بيانات القسم بنجاح', 'success')
    return redirect(url_for('departments'))

@app.route('/delete_department/<int:id>')
@login_required
def delete_department(id):
    if current_user.role != 'مدير':
        flash('غير مصرح لك بحذف الأقسام', 'danger')
        return redirect(url_for('departments'))
    
    department = Department.query.get_or_404(id)
    if department.documents or department.users:
        flash('لا يمكن حذف القسم لوجود مستندات أو مستخدمين مرتبطين به', 'danger')
        return redirect(url_for('departments'))
    
    db.session.delete(department)
    db.session.commit()
    
    flash('تم حذف القسم بنجاح', 'success')
    return redirect(url_for('departments'))

@app.route('/download/<int:document_id>')
@login_required
def download_document(document_id):
    """Download a document file"""
    document = Document.query.get_or_404(document_id)
    
    # Log the download activity
    create_activity(
        user_id=current_user.id,
        document_id=document.id,
        action='download',
        description=f'تم تحميل المستند: {document.title}'
    )
    
    # Return the file from the uploads directory
    return send_from_directory(
        app.config['UPLOAD_FOLDER'],
        document.file_path,
        as_attachment=True,
        download_name=os.path.basename(document.file_path)
    )

@app.route('/documents/<int:document_id>')
@login_required
def view_document(document_id):
    """View a document's details"""
    document = Document.query.get_or_404(document_id)
    
    # Create activity log
    create_activity(
        user_id=current_user.id,
        document_id=document.id,
        action='view',
        description=f'تم عرض المستند: {document.title}'
    )
    
    return render_template('documents.html', document=document)

@app.route('/documents')
@login_required
def documents():
    page = request.args.get('page', 1, type=int)
    query = Document.query
    
    # Filter by department unless user is admin
    if current_user.role != 'مدير':
        query = query.filter_by(department_id=current_user.department_id)
    
    # Filter by user permissions
    if current_user.role != 'مدير':
        permitted_types = []
        if current_user.has_permission('وارد'):
            permitted_types.append('incoming')
        if current_user.has_permission('صادر'):
            permitted_types.append('outgoing')
        if permitted_types:
            query = query.filter(Document.document_type.in_(permitted_types))
        else:
            query = query.filter(False)  # No permissions, return empty result
    
    # Apply filters
    doc_type = request.args.get('document_type')
    category = request.args.get('category')
    status = request.args.get('status')
    department_id = request.args.get('department_id')
    source_dept = request.args.get('source_department')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    if doc_type:
        query = query.filter_by(document_type=doc_type)
    if category:
        query = query.filter_by(category=category)
    if status:
        query = query.filter_by(status=status)
    if department_id and current_user.role == 'مدير':
        query = query.filter_by(department_id=department_id)
    if source_dept and doc_type == 'incoming':
        query = query.filter_by(source_department=source_dept)
    if start_date and doc_type == 'incoming':
        query = query.filter(Document.incoming_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
    elif start_date:
        query = query.filter(Document.document_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
    if end_date and doc_type == 'incoming':
        query = query.filter(Document.incoming_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
    elif end_date:
        query = query.filter(Document.document_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
    
    documents = query.order_by(Document.created_at.desc()).paginate(page=page, per_page=10)
    departments = Department.query.all()
    return render_template('documents.html', documents=documents, departments=departments)

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    # Check if user has permission to upload documents
    doc_type = request.form.get('document_type') if request.method == 'POST' else None
    if doc_type == 'incoming' and not current_user.has_permission('وارد'):
        flash('غير مصرح لك برفع المستندات الواردة', 'danger')
        return redirect(url_for('documents'))
    elif doc_type == 'outgoing' and not current_user.has_permission('صادر'):
        flash('غير مصرح لك برفع المستندات الصادرة', 'danger')
        return redirect(url_for('documents'))
    elif doc_type == 'archive' and not current_user.has_permission('أرشفة'):
        flash('غير مصرح لك برفع المستندات المؤرشفة', 'danger')
        return redirect(url_for('documents'))
    
    if request.method == 'POST':
        title = request.form.get('title')
        document_type = request.form.get('document_type')
        file = request.files.get('document_file')
        
        if file and allowed_file(file.filename):
            # Create folder structure based on department and document type
            dept_folder = os.path.join(app.config['UPLOAD_FOLDER'], str(current_user.department_id))
            type_folder = os.path.join(dept_folder, document_type)
            
            # Create folders if they don't exist
            for folder in [dept_folder, type_folder]:
                if not os.path.exists(folder):
                    os.makedirs(folder)
            
            filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
            file_path = os.path.join(type_folder, filename)
            file.save(file_path)
            
            document = Document(
                title=title,
                description=request.form.get('description'),
                file_path=os.path.join(str(current_user.department_id), document_type, filename),
                document_type=document_type,
                category=request.form.get('category'),
                tags=request.form.get('tags'),
                user_id=current_user.id,
                department_id=current_user.department_id,
                # Common fields for both incoming and outgoing documents
                document_number=request.form.get('document_number'),
                document_date=datetime.strptime(request.form.get('document_date'), '%Y-%m-%d').date() if request.form.get('document_date') else None,
                subject=request.form.get('subject'),
                has_attachments='has_attachments' in request.form
            )
            
            # Handle document type specific fields
            if document_type == 'incoming':
                document.incoming_date = datetime.strptime(request.form.get('incoming_date'), '%Y-%m-%d').date()
                document.source_department = request.form.get('source_department')
                document.document_number = request.form.get('document_number')
                document.document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d').date()
                document.subject = request.form.get('subject')
                document.department_id = request.form.get('department_id') or current_user.department_id
            elif document_type == 'outgoing':
                document.document_date = datetime.strptime(request.form.get('outgoing_date'), '%Y-%m-%d').date()
                document.document_number = request.form.get('outgoing_number')
                document.destination_department = request.form.get('destination_department')
                document.subject = request.form.get('outgoing_subject')
            elif document_type == 'archive':
                document.archive_number = request.form.get('archive_number')
                document.archive_type = request.form.get('archive_type')
            
            try:
                # Save document first
                db.session.add(document)
                db.session.commit()
                
                # Create activity log with proper description
                if document_type == 'incoming':
                    action_desc = f"تم رفع مستند وارد جديد: {title}"
                elif document_type == 'outgoing':
                    action_desc = f"تم رفع مستند صادر جديد: {title}"
                else:
                    action_desc = f"تم أرشفة مستند جديد: {title}"
                
                # Create activity only if document was saved successfully
                if document.id:
                    create_activity(current_user.id, document.id, 'upload', action_desc)
            except Exception as e:
                flash(f'حدث خطأ أثناء حفظ المستند: {str(e)}', 'danger')
                return redirect(url_for('documents'))
                
            flash(f'تم رفع المستند {document_type} بنجاح', 'success')
            return redirect(url_for('documents'))
            
        flash('حدث خطأ أثناء رفع الملف', 'danger')
    
    departments = Department.query.all()
    return render_template('upload.html', departments=departments)

@app.route('/search')
@login_required
def search():
    query_text = request.args.get('query', '')
    if query_text:
        # Base query
        query = Document.query
        
        # Filter by department unless user is admin
        if current_user.role != 'مدير':
            query = query.filter_by(department_id=current_user.department_id)
        
        # Filter by user permissions
        if current_user.role != 'مدير':
            permitted_types = []
            if current_user.has_permission('وارد'):
                permitted_types.append('incoming')
            if current_user.has_permission('صادر'):
                permitted_types.append('outgoing')
            if permitted_types:
                query = query.filter(Document.document_type.in_(permitted_types))
            else:
                query = query.filter(False)  # No permissions, return empty result
        
        # Perform search
        results = query.filter(
            db.or_(
                Document.title.contains(query_text),
                Document.description.contains(query_text),
                Document.document_number.contains(query_text),
                Document.source_department.contains(query_text),
                Document.subject.contains(query_text),
                Document.tags.contains(query_text)
            )
        ).order_by(Document.created_at.desc()).all()
        return render_template('search.html', results=results, query=query_text)
    return render_template('search.html')

@app.route('/profile')
@login_required
def profile():
    query = Document.query.filter_by(user_id=current_user.id)
    if current_user.role != 'مدير':
        query = query.filter_by(department_id=current_user.department_id)
    documents_count = query.count()
    
    activities = Activity.query.filter_by(user_id=current_user.id)\
        .order_by(Activity.timestamp.desc()).limit(10).all()
    activities_count = Activity.query.filter_by(user_id=current_user.id).count()
    
    return render_template('profile.html',
                         documents_count=documents_count,
                         activities_count=activities_count,
                         activities=activities)

@app.route('/document/<int:id>/details')
@login_required
def document_details(id):
    document = Document.query.get_or_404(id)
    
    # Check if user has permission to view this document
    if current_user.role != 'مدير' and document.department_id != current_user.department_id:
        abort(403)
    
    return jsonify({
        'id': document.id,
        'title': document.title,
        'document_type': document.document_type,
        'document_number': document.document_number,
        'document_date': document.document_date.strftime('%Y-%m-%d') if document.document_date else None,
        'subject': document.subject,
        'department': document.department.name if document.department else None,
        'has_attachments': document.has_attachments,
        # Incoming specific fields
        'incoming_date': document.incoming_date.strftime('%Y-%m-%d') if document.incoming_date else None,
        'source_department': document.source_department,
        # Outgoing specific fields
        'destination_department': document.destination_department,
        # Archive specific fields
        'archive_number': document.archive_number,
        'archive_type': document.archive_type
    })

@app.route('/document/<int:id>')
@login_required
def view_document(id):
    document = Document.query.get_or_404(id)
    
    # Check if user has permission to view this document
    if current_user.role != 'مدير' and document.department_id != current_user.department_id:
        abort(403)
    
    if document.document_type == 'incoming' and not current_user.has_permission('وارد'):
        abort(403)
    elif document.document_type == 'outgoing' and not current_user.has_permission('صادر'):
        abort(403)
    
    try:
        create_activity(current_user.id, document.id, 'view', 'تم عرض المستند')
    except Exception as e:
        # Log error but don't prevent viewing
        print(f"Error creating view activity: {e}")
        
    return jsonify({
        'title': document.title,
        'description': document.description,
        'document_type': document.document_type,
        'category': document.category,
        'document_number': document.document_number,
        'source_department': document.source_department,
        'subject': document.subject,
        'document_date': document.document_date.strftime('%Y-%m-%d') if document.document_date else None,
        'incoming_date': document.incoming_date.strftime('%Y-%m-%d') if document.incoming_date else None,
        'has_attachments': document.has_attachments,
        'created_at': document.created_at.strftime('%Y-%m-%d %H:%M'),
        'status': document.status
    })

@app.route('/document/download/<int:id>')
@login_required
def download_document(id):
    document = Document.query.get_or_404(id)
    
    # Check if user has permission to download this document
    if current_user.role != 'مدير' and document.department_id != current_user.department_id:
        abort(403)
    
    if document.document_type == 'incoming' and not current_user.has_permission('وارد'):
        abort(403)
    elif document.document_type == 'outgoing' and not current_user.has_permission('صادر'):
        abort(403)
    
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], document.file_path)
    
    if not os.path.exists(file_path):
        abort(404)
    
    try:
        create_activity(current_user.id, document.id, 'download', 'تم تحميل المستند')
    except Exception as e:
        # Log error but don't prevent download
        print(f"Error creating download activity: {e}")
    
    return send_file(
        file_path,
        as_attachment=True,
        download_name=secure_filename(document.title + os.path.splitext(document.file_path)[1])
    )

if __name__ == '__main__':
    with app.app_context():
        # Drop all tables and create them again
        db.drop_all()
        db.create_all()
        
        # Create some default departments
        departments = [
            Department(name='الإدارة العامة', code='HQ'),
            Department(name='ثورة العشرين', code='200'),
            Department(name='الجوادين', code='221'),
            Department(name='مطار بغداد الدولي', code='242'),
            Department(name='الحبيبية', code='244'),
            Department(name='الزعفرانية', code='198'),
            Department(name='حي سومر', code='194'),
            Department(name='الجامعة التكنولوجية', code='184'),
            Department(name='الشعلة', code='180'),
            Department(name='الملعب', code='175'),
            Department(name='المعارض الحديثة', code='263'),
            Department(name='الاعتماد التجاري', code='506'),
            Department(name='الدورة', code='501'),
            Department(name='المتنبي', code='253'),
            Department(name='دجلة', code='245'),
            Department(name='الصالحية', code='33'),
            Department(name='ساحة الوثبة', code='46'),
            Department(name='الشورجة', code='42'),
            Department(name='ساحة السباع', code='40'),
            Department(name='الامام الاعظم', code='151'),
            Department(name='ارخيتة', code='26'),
            Department(name='الكرخ', code='23'),
            Department(name='المرتضى', code='11'),
            Department(name='البياع', code='95'),
            Department(name='السعدون', code='109'),
            Department(name='الرئيسي', code='106'),
            Department(name='جميلة', code='54'),
            Department(name='راغبة خاتون', code='97'),
            Department(name='المحمودية', code='74'),
            Department(name='اليرموك', code='71'),
            Department(name='كلية الرافدين', code='597'),
            Department(name='معرض بغداد الدولي', code='528'),
            Department(name='العدالة', code='586'),
            Department(name='حي الربيع', code='581'),
            Department(name='ابي غريب', code='507'),
            Department(name='الطالبية', code='520'),
            Department(name='وزارة النقل والمواصلات', code='534'),
            Department(name='وزارة الخارجية', code='533'),
            Department(name='مكتب مدينة الطب', code='532'),
            Department(name='وزارة المالية', code='530'),
            Department(name='الرباط', code='524'),
            Department(name='شبكة الاعلام العراقي', code='542'),
            Department(name='مكتب دائرة الكهرباء', code='554'),
            Department(name='الشركة العامة للبطاريات', code='567'),
            Department(name='المسبح', code='558'),
            Department(name='المأمون', code='538'),
            Department(name='دائرة التقاعد العامة', code='551'),
            Department(name='وزارة العلوم والتكنلوجيا', code='544'),
            Department(name='الجامعة المستنصرية', code='543'),
            Department(name='الشركة العامة للزيوت', code='557'),
            Department(name='السيدية', code='286'),
            # Adding new departments
            Department(name='القسم الاداري', code='338'),
            Department(name='القسم الدولي', code='333'),
            Department(name='القسم القانوني', code='339'),
            Department(name='القسم المالي', code='340'),
            Department(name='القسم الهندسي', code='336'),
            Department(name='المدير العام', code='341'),
            Department(name='المدير العام\\ النظام الشامل المصرفي', code='379'),
            Department(name='المدير العام\\ وحدة غسيل اموال', code='316'),
            Department(name='المدير العام\\مكتب اعادة الهيكيلة', code='373'),
            Department(name='المدير العام\\مكتب مراقب الامتثال', code='2040'),
            Department(name='قسم ادارة المخاطر', code='374'),
            Department(name='قسم الائتمان', code='376'),
            Department(name='قسم الاحصاء', code='342'),
            Department(name='قسم البطاقات', code='540'),
            Department(name='قسم الحاسبة الالكترونية', code='309'),
            Department(name='قسم الخزينة', code='577'),
            Department(name='قسم الدراسات والعمليات المصرفية', code='306'),
            Department(name='قسم الرقابة', code='345'),
            Department(name='قسم الموارد البشرية', code='305')
        ]
        for dept in departments:
            db.session.add(dept)
        db.session.commit()
        
        # Create admin user if not exists
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                fullname='System Administrator',  # Added fullname field
                password=generate_password_hash('admin123'),
                email='<EMAIL>',
                role='مدير',
                department_id=1,  # HQ
                permissions='صادر,وارد,أرشفة',
                is_active=True
            )
            db.session.add(admin)
            db.session.commit()
            print("Admin user and default departments created successfully")
    
    app.run(debug=True)