{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">لوحة التحكم</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('upload') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مستند جديد
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">المستندات الواردة</h6>
                            <h2 class="mb-0">{{ incoming_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-inbox fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">المستندات الصادرة</h6>
                            <h2 class="mb-0">{{ outgoing_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-paper-plane fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">قيد المعالجة</h6>
                            <h2 class="mb-0">{{ pending_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">تم المعالجة</h6>
                            <h2 class="mb-0">{{ completed_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Documents and Activities -->
    <div class="row">
        <!-- Recent Documents -->
        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">آخر المستندات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>النوع</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in recent_documents|default([]) %}
                                <tr>
                                    <td>{{ doc.title }}</td>
                                    <td>
                                        {% if doc.document_type == 'incoming' %}
                                        <span class="badge bg-primary">وارد</span>
                                        {% else %}
                                        <span class="badge bg-success">صادر</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        {% if doc.status == 'pending' %}
                                        <span class="badge bg-warning">قيد المعالجة</span>
                                        {% elif doc.status == 'completed' %}
                                        <span class="badge bg-success">تم المعالجة</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="#" class="btn btn-sm btn-info" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">آخر النشاطات</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        {% for activity in recent_activities|default([]) %}
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                <div class="timeline-icon bg-light rounded-circle p-2">
                                    <i class="fas fa-file-alt text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0">{{ activity.action }}</h6>
                                <small class="text-muted">
                                    {{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                </small>
                                <p class="mb-0">{{ activity.notes }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}