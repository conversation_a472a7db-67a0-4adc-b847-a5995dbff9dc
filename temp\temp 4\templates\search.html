{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">بحث متقدم</h1>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <!-- Text Search -->
                <div class="col-12">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchQuery" name="query" 
                               placeholder="ابحث في عنوان المستند، الوصف، المحتوى...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="col-md-3">
                    <label class="form-label" for="document_type">نوع المستند</label>
                    <select class="form-select" id="document_type" name="document_type" aria-label="نوع المستند">
                        <option value="">الكل</option>
                        <option value="incoming">وارد</option>
                        <option value="outgoing">صادر</option>
                        <option value="archive">ارشفة</option>
                    </select>
                </div>


                <div class="col-md-3">
                    <label class="form-label" for="user">المسؤول</label>
                    <select class="form-select" id="user" name="user" aria-label="المسؤول">
                        <option value="">الكل</option>
                        {% for user in users|default([]) %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Date Range -->
                <div class="col-md-3">
                    <label class="form-label" for="date_from">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" aria-label="من تاريخ">
                </div>

                <div class="col-md-3">
                    <label class="form-label" for="date_to">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" aria-label="إلى تاريخ">
                </div>

                <div class="col-md-6">
                    <label class="form-label" for="tags">الوسوم</label>
                    <input type="text" class="form-control" id="tags" name="tags"
                           placeholder="أدخل الوسوم مفصولة بفواصل" aria-label="الوسوم">
                </div>

                <!-- Additional Filters -->
                <div class="col-12">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="searchContent" name="search_content">
                        <label class="form-check-label" for="searchContent">
                            البحث في محتوى المستندات
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="caseSensitive" name="case_sensitive">
                        <label class="form-check-label" for="caseSensitive">
                            مطابقة حالة الأحرف
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">نتائج البحث</h5>
            <div class="btn-group">
                <a href="{{ url_for('download_results', **request.args) }}" class="btn btn-sm btn-outline-secondary" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
              
                </a>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="searchResults">
                <!-- Results will be loaded here -->
                <div class="table-responsive">
                    <div style="max-width: 100%; overflow-x: auto;">
                        <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>رقم الكتاب</th>
                                <th>رقم الأرشفة</th>
                                <th>نوع الأرشفة</th>
                                <th>تاريخ الاستلام</th>
                                <th>الجهة</th>
                                <th>المسؤول</th>
                                <th class="actions-column">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="resultsBody">
                            {% for doc in results|default([]) %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        {{ doc.title }}
                                        {% if doc.highlight %}
                                        <small class="text-muted ms-2">(تطابق: {{ doc.highlight }})</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                    <span class="badge bg-primary">وارد</span>
                                    {% elif doc.document_type == 'outgoing' %}
                                    <span class="badge bg-success">صادر</span>
                                    {% else %}
                                    <span class="badge bg-info">ارشفة</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>{{ doc.document_number or '-' }}</td>
                                <td>{{ doc.archive_number or '-' if doc.document_type == 'archive' else '-' }}</td>
                                <td>{{ doc.archive_type or '-' if doc.document_type == 'archive' else '-' }}</td>
                                <td>{{ doc.incoming_date.strftime('%Y-%m-%d') if doc.document_type == 'incoming' and doc.incoming_date else '-' }}</td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                        {{ doc.source_department or '-' }}
                                    {% elif doc.document_type == 'outgoing' %}
                                        {{ doc.destination_department or '-' }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>{{ doc.user.username }}</td>
                                <td class="actions-column">
                                    <div class="btn-group">
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='view') }}"
                                           class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='download') }}"
                                           class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% if current_user.role == 'مدير' or doc.user_id == current_user.id %}
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='edit') }}"
                                           class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('document_action', document_id=doc.id, action='delete') }}"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');"
                                           class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- No Results Message -->
                {% if not results|default(False) %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب تعديل معايير البحث</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    .card-header {
        border-bottom: 2px solid #000;
    }
    
    .table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
    }
    
    .table thead th {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
    }
    
    .badge {
        border: 1px solid #000;
        padding: 0.25em 0.5em;
    }
    
    .container-fluid {
        width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .btn-group,
    .form-check,
    .searchForm,
    .actions-column {
        display: none !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    const searchForm = document.getElementById('searchForm');
    const resultsBody = document.getElementById('resultsBody');
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        document.getElementById('searchResults').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-2">جاري البحث...</p>
            </div>
        `;

        // Collect form data
        const formData = new FormData(searchForm);
        const searchParams = new URLSearchParams();
        for (let [key, value] of formData) {
            searchParams.append(key, value);
        }

        // Make AJAX request
        fetch(`/search?${searchParams.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(results => {
            if (results.length === 0) {
                document.getElementById('searchResults').innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نتائج</h5>
                        <p class="text-muted">جرب تعديل معايير البحث</p>
                    </div>
                `;
                return;
            }

            // Build results table
            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>التاريخ</th>
                                <th>رقم الكتاب</th>
                                <th>رقم الأرشفة</th>
                                <th>نوع الأرشفة</th>
                                <th>تاريخ الاستلام</th>
                                <th>الجهة</th>
                                <th>المسؤول</th>
                                <th class="actions-column">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            results.forEach(doc => {
                let sourceOrDestination = '-';
                if (doc.document_type === 'incoming') {
                    sourceOrDestination = doc.source_department || '-';
                } else if (doc.document_type === 'outgoing') {
                    sourceOrDestination = doc.destination_department || '-';
                }

                tableHtml += `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-alt text-primary me-2"></i>
                                ${doc.title}
                            </div>
                        </td>
                        <td>
                            ${doc.document_type === 'incoming'
                                ? '<span class="badge bg-primary">وارد</span>'
                                : doc.document_type === 'outgoing'
                                    ? '<span class="badge bg-success">صادر</span>'
                                    : '<span class="badge bg-info">ارشفة</span>'}
                        </td>
                        <td>${doc.created_at}</td>
                        <td>${doc.document_number || '-'}</td>
                        <td>${doc.document_type === 'archive' ? (doc.archive_number || '-') : '-'}</td>
                        <td>${doc.document_type === 'archive' ? (doc.archive_type || '-') : '-'}</td>
                        <td>${doc.document_type === 'incoming' && doc.incoming_date ? doc.incoming_date : '-'}</td>
                        <td>${sourceOrDestination}</td>
                        <td>${doc.user}</td>
                        <td class="actions-column">
                            ${getActionButtons(doc)}
                        </td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            document.getElementById('searchResults').innerHTML = tableHtml;
            // Initialize tooltips for the new content
            initTooltips();
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('searchResults').innerHTML = `
                <div class="alert alert-danger">
                    حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.
                </div>
            `;
        });
    });
    // Helper functions for action buttons
    function getActionButtons(doc) {
        return `
            <div class="btn-group">
                <a href="/document/${doc.id}/view"
                   class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                    <i class="fas fa-eye"></i>
                </a>
                <a href="/document/${doc.id}/download"
                   class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تحميل">
                    <i class="fas fa-download"></i>
                </a>
                ${doc.is_owner || doc.is_admin ? `
                    <a href="/document/${doc.id}/edit"
                       class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="/document/${doc.id}/delete"
                       onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');"
                       class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف">
                        <i class="fas fa-trash"></i>
                    </a>
                ` : ''}
            </div>
        `;
    }

    function initTooltips() {
        // Destroy existing tooltips first
        var tooltips = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltips.forEach(function(element) {
            var tooltip = bootstrap.Tooltip.getInstance(element);
            if (tooltip) {
                tooltip.dispose();
            }
        });
        
        // Initialize new tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Call initTooltips when page loads
    initTooltips();

    // Add event handler for document actions
    document.addEventListener('click', function(e) {
        if (e.target.closest('.doc-action')) {
            const button = e.target.closest('.doc-action');
            const action = button.dataset.action;
            const docId = button.dataset.id;
            
            if (action === 'delete' && !confirm('هل أنت متأكد من حذف هذا المستند؟')) {
                return;
            }
            
            window.location.href = `/document/${docId}/${action}`;
        }
    });

    // Filter Form Reset
    document.querySelector('button[type="reset"]').addEventListener('click', function() {
        setTimeout(() => {
            filterForm.dispatchEvent(new Event('submit'));
        }, 0);
    });
    
    // Re-initialize tooltips after AJAX updates
    function initTooltips() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});
</script>
{% endblock %}