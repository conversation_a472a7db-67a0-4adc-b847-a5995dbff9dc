"""
Document model
"""
from datetime import datetime, UTC
from .database import db

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    file_path = db.Column(db.String(500), nullable=False)
    document_type = db.Column(db.String(20), nullable=False)  # 'incoming', 'outgoing', or 'archive'
    document_number = db.Column(db.String(50))  # For outgoing documents
    document_date = db.Column(db.Date)
    subject = db.Column(db.String(200))
    source_department = db.Column(db.String(100))  # For incoming documents
    destination_department = db.Column(db.String(100))  # For outgoing documents
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(UTC))
    incoming_date = db.Column(db.Date)  # For incoming documents
    
    # Archive specific fields
    archive_number = db.Column(db.String(50))  # For archived documents
    archive_type = db.Column(db.String(100))   # For archived documents
    
    # Tags for easier search
    tags = db.Column(db.String(500))  # Comma-separated tags
    
    # Foreign keys
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=False)

    # Define relationships
    department = db.relationship('Department', back_populates='documents')
    activities = db.relationship('Activity', back_populates='document', lazy=True)
    created_by = db.relationship('User', back_populates='documents')

    @property
    def recipient_department_email(self):
        """Get the email of the destination department"""
        if not self.destination_department:
            return None
        from .department import Department
        dept = Department.query.filter_by(name=self.destination_department).first()
        return dept.email if dept else None

    def validate(self):
        """Validate document attributes based on type"""
        if self.document_type not in ['incoming', 'outgoing', 'archive']:
            raise ValueError('نوع المستند غير صالح')
            
        if self.document_type == 'incoming':
            if not self.incoming_date:
                raise ValueError('تاريخ الوارد مطلوب')
            if not self.source_department:
                raise ValueError('الجهة المرسلة مطلوبة')
            if not self.document_date:
                raise ValueError('تاريخ المستند مطلوب')
                
        elif self.document_type == 'outgoing':
            if not self.document_date:
                raise ValueError('تاريخ الصادر مطلوب')
            if not self.document_number:
                raise ValueError('رقم الكتاب مطلوب')
            if not self.destination_department:
                raise ValueError('الجهة المرسل إليها مطلوبة')
                
        elif self.document_type == 'archive':
            if not self.archive_number:
                raise ValueError('رقم الأرشفة مطلوب')
            if not self.archive_type:
                raise ValueError('نوع الأرشفة مطلوب')
                
        if not self.subject:
            raise ValueError('الموضوع مطلوب')
            
    def __repr__(self):
        return f'<Document {self.title}>'