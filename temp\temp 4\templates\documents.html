{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">المستندات</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('upload') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة مستند جديد
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label" for="filter_document_type">نوع المستند</label>
                    <select class="form-select" id="filter_document_type" name="document_type" aria-label="نوع المستند">
                        <option value="">الكل</option>
                        <option value="incoming">وارد</option>
                        <option value="outgoing">صادر</option>
                        <option value="outgoing">ارشفة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label" for="filter_date">التاريخ</label>
                    <input type="date" class="form-control" id="filter_date" name="date" aria-label="تاريخ المستند">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> تصفية
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Documents Table -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll"
                                           aria-label="تحديد الكل">
                                    <label class="form-check-label visually-hidden" for="selectAll">
                                        تحديد الكل
                                    </label>
                                </div>
                            </th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>رقم الكتاب</th>
                            <th>التاريخ</th>
                            <th>الجهة</th>
                            <th>القسم</th>
                            <th>المسؤول</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in documents|default([]) %}
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="doc_{{ doc.id }}"
                                           value="{{ doc.id }}" aria-label="تحديد المستند {{ doc.title }}">
                                    <label class="form-check-label visually-hidden" for="doc_{{ doc.id }}">
                                        تحديد المستند {{ doc.title }}
                                    </label>
                                </div>
                            </td>
                            <td>{{ doc.title }}</td>
                            <td>
                                {% if doc.document_type == 'incoming' %}
                                <span class="badge bg-primary">وارد</span>
                                {% elif doc.document_type == 'outgoing' %}
                                <span class="badge bg-success">صادر</span>
                                {% else %}
                                <span class="badge bg-info">أرشيف</span>
                                {% endif %}
                            </td>
                            <td>{{ doc.document_number }}</td>
                            <td>
                                {% if doc.document_type == 'incoming' %}
                                    {% if doc.incoming_date %}
                                        {{ doc.incoming_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                {% else %}
                                    {% if doc.document_date %}
                                        {{ doc.document_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        -
                                    {% endif %}
                                {% endif %}
                            </td>
                            <td>
                                {% if doc.document_type == 'incoming' %}
                                {{ doc.source_department }}
                                {% elif doc.document_type == 'outgoing' %}
                                {{ doc.destination_department }}
                                {% endif %}
                            </td>
                            <td>{{ doc.created_by.username }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('document_action', document_id=doc.id, action='view') }}"
                                       class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('document_action', document_id=doc.id, action='download') }}"
                                       class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% if current_user.role == 'مدير' or doc.user_id == current_user.id %}
                                    <a href="{{ url_for('document_action', document_id=doc.id, action='edit') }}"
                                       class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{{ url_for('document_action', document_id=doc.id, action='delete') }}"
                                       class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="حذف"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">السابق</a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">التالي</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Document View Modal -->
<div class="modal fade" id="documentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentModalTitle">تفاصيل المستند</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        title="إغلاق" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات عامة</h6>
                        <dl class="row">
                            <dt class="col-sm-4">العنوان:</dt>
                            <dd class="col-sm-8" id="docTitle"></dd>
                            
                            <dt class="col-sm-4">النوع:</dt>
                            <dd class="col-sm-8" id="docType"></dd>
                            
                            <dt class="col-sm-4">رقم الكتاب:</dt>
                            <dd class="col-sm-8" id="docNumber"></dd>
                            
                            <dt class="col-sm-4">التاريخ:</dt>
                            <dd class="col-sm-8" id="docDate"></dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <h6>تفاصيل إضافية</h6>
                        <dl class="row">
                            <dt class="col-sm-4 incoming-field">تاريخ الاستلام:</dt>
                            <dd class="col-sm-8" id="docIncomingDate"></dd>
                            
                            <dt class="col-sm-4 incoming-field">الجهة الواردة:</dt>
                            <dd class="col-sm-8" id="docSourceDept"></dd>
                            
                            <dt class="col-sm-4 outgoing-field">الجهة المرسل إليها:</dt>
                            <dd class="col-sm-8" id="docDestDept"></dd>
                            
                            <dt class="col-sm-4">القسم:</dt>
                            <dd class="col-sm-8" id="docDepartment"></dd>
                        </dl>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>الموضوع</h6>
                        <p id="docSubject"></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>المرفقات</h6>
                        <div id="docAttachments">
                            <p class="text-muted">لا توجد مرفقات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select All Checkbox
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('tbody .form-check-input');

    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
    });

    // Filter Form
    const filterForm = document.getElementById('filterForm');
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        // Handle filter submission
        const formData = new FormData(filterForm);
        // You would typically make an AJAX request here
    });

    // Document Actions
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    
        // Handle document actions
        document.addEventListener('click', function(e) {
            if (e.target.closest('[data-action]')) {
                const button = e.target.closest('[data-action]');
                const action = button.dataset.action;
                const docId = button.dataset.id;
                
                if (action === 'delete' && !confirm('هل أنت متأكد من حذف هذا المستند؟')) {
                    e.preventDefault();
                    return;
                }
            }
        });
    });
});
</script>
{% endblock %}