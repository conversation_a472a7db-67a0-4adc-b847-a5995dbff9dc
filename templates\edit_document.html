{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{{ 'تعديل المستند' if edit_mode else 'تفاصيل المستند' }}</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('documents') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للمستندات
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            {% if edit_mode %}
            <form method="POST">
            {% endif %}
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h5>معلومات عامة</h5>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            {% if edit_mode %}
                            <input type="text" class="form-control" id="title" name="title" value="{{ document.title }}" required
                                   aria-label="عنوان المستند" title="عنوان المستند">
                            {% else %}
                            <p class="form-control-plaintext">{{ document.title }}</p>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label class="form-label">نوع المستند</label>
                            <p class="form-control-plaintext">
                                {% if document.document_type == 'incoming' %}
                                <span class="badge bg-primary">وارد</span>
                                {% elif document.document_type == 'outgoing' %}
                                <span class="badge bg-success">صادر</span>
                                {% else %}
                                <span class="badge bg-info">أرشيف</span>
                                {% endif %}
                            </p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">رقم الكتاب</label>
                            <p class="form-control-plaintext">{{ document.document_number or '-' }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">تاريخ المستند</label>
                            <p class="form-control-plaintext">
                                {{ document.document_date.strftime('%Y-%m-%d') if document.document_date else '-' }}
                            </p>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h5>تفاصيل إضافية</h5>
                        {% if document.document_type == 'incoming' %}
                        <div class="mb-3">
                            <label class="form-label">تاريخ الاستلام</label>
                            <p class="form-control-plaintext">
                                {{ document.incoming_date.strftime('%Y-%m-%d') if document.incoming_date else '-' }}
                            </p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الجهة المرسلة</label>
                            <p class="form-control-plaintext">{{ document.source_department or '-' }}</p>
                        </div>
                        {% elif document.document_type == 'outgoing' %}
                        <div class="mb-3">
                            <label class="form-label">الجهة المرسل إليها</label>
                            <p class="form-control-plaintext">{{ document.destination_department or '-' }}</p>
                        </div>
                        {% elif document.document_type == 'archive' %}
                        <div class="mb-3">
                            <label class="form-label">رقم الأرشفة</label>
                            <p class="form-control-plaintext">{{ document.archive_number or '-' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الأرشفة</label>
                            <p class="form-control-plaintext">{{ document.archive_type or '-' }}</p>
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label class="form-label">القسم</label>
                            <p class="form-control-plaintext">{{ document.department.name if document.department else '-' }}</p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">المسؤول</label>
                            <p class="form-control-plaintext">{{ document.user.username if document.user else '-' }}</p>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="subject" class="form-label">الموضوع</label>
                    {% if edit_mode %}
                    <textarea class="form-control" id="subject" name="subject" rows="3"
                              aria-label="موضوع المستند" title="موضوع المستند">{{ document.subject }}</textarea>
                    {% else %}
                    <p class="form-control-plaintext">{{ document.subject or '-' }}</p>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">الوصف</label>
                    {% if edit_mode %}
                    <textarea class="form-control" id="description" name="description" rows="3"
                              aria-label="وصف المستند" title="وصف المستند">{{ document.description }}</textarea>
                    {% else %}
                    <p class="form-control-plaintext">{{ document.description or '-' }}</p>
                    {% endif %}
                </div>

                <!-- Categories Section -->
                <div class="mb-3">
                    <label class="form-label">التصنيفات</label>
                    {% if edit_mode %}
                    <select class="form-select" id="categories" name="categories" multiple
                            aria-label="تصنيفات المستند" title="تصنيفات المستند">
                        {% for category in categories %}
                            <option value="{{ category.id }}"
                                    {% if category in document.categories %}selected{% endif %}>
                                {{ category.full_path }}
                            </option>
                        {% endfor %}
                    </select>
                    <small class="form-text text-muted">يمكنك اختيار أكثر من تصنيف</small>
                    {% else %}
                    <p class="form-control-plaintext">
                        {% if document.categories %}
                            {% for category in document.categories %}
                                <span class="badge bg-secondary me-1">{{ category.full_path }}</span>
                            {% endfor %}
                        {% else %}
                            -
                        {% endif %}
                    </p>
                    {% endif %}
                </div>

                <!-- Tags Section -->
                <div class="mb-3">
                    <label for="tags" class="form-label">الكلمات المفتاحية</label>
                    {% if edit_mode %}
                    <input type="text" class="form-control" id="tags" name="tags"
                           value="{{ ','.join(document.tags) if document.tags else '' }}"
                           aria-label="الكلمات المفتاحية" title="الكلمات المفتاحية"
                           placeholder="أدخل الكلمات المفتاحية مفصولة بفواصل">
                    <small class="form-text text-muted">أدخل الكلمات المفتاحية مفصولة بفواصل</small>
                    {% else %}
                    <p class="form-control-plaintext">
                        {% if document.tags %}
                            {% for tag in document.tags %}
                                <span class="badge bg-info me-1">{{ tag }}</span>
                            {% endfor %}
                        {% else %}
                            -
                        {% endif %}
                    </p>
                    {% endif %}
                </div>

                {% if document.file_path %}
                <div class="mb-3">
                    <label class="form-label">المرفقات</label>
                    <div>
                        <a href="{{ url_for('document_action', document_id=document.id, action='download') }}"
                           class="btn btn-primary">
                            <i class="fas fa-download"></i> تحميل الملف
                        </a>
                    </div>
                </div>
                {% endif %}

                {% if edit_mode %}
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
                </form>
                {% endif %}
        </div>
    </div>
</div>
{% endblock %}