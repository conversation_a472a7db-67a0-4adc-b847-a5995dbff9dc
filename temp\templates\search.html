{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">بحث متقدم</h1>
    </div>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="searchForm" class="row g-3">
                <!-- Text Search -->
                <div class="col-12">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchQuery" name="query" 
                               placeholder="ابحث في عنوان المستند، الوصف، المحتوى...">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters -->
                <div class="col-md-3">
                    <label class="form-label">نوع المستند</label>
                    <select class="form-select" name="document_type">
                        <option value="">الكل</option>
                        <option value="incoming">وارد</option>
                        <option value="outgoing">صادر</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">التصنيف</label>
                    <select class="form-select" name="category">
                        <option value="">الكل</option>
                        <option value="official">رسمي</option>
                        <option value="internal">داخلي</option>
                        <option value="external">خارجي</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">الكل</option>
                        <option value="pending">قيد المعالجة</option>
                        <option value="in_progress">جاري المعالجة</option>
                        <option value="completed">تم المعالجة</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label class="form-label">المسؤول</label>
                    <select class="form-select" name="user">
                        <option value="">الكل</option>
                        {% for user in users|default([]) %}
                        <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Date Range -->
                <div class="col-md-3">
                    <label class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" name="date_from">
                </div>

                <div class="col-md-3">
                    <label class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" name="date_to">
                </div>

                <div class="col-md-6">
                    <label class="form-label">الوسوم</label>
                    <input type="text" class="form-control" name="tags" placeholder="أدخل الوسوم مفصولة بفواصل">
                </div>

                <!-- Additional Filters -->
                <div class="col-12">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="searchContent" name="search_content">
                        <label class="form-check-label" for="searchContent">
                            البحث في محتوى المستندات
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="caseSensitive" name="case_sensitive">
                        <label class="form-check-label" for="caseSensitive">
                            مطابقة حالة الأحرف
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Search Results -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">نتائج البحث</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-outline-secondary" title="تصدير إلى Excel">
                    <i class="fas fa-file-excel"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" title="تصدير إلى PDF">
                    <i class="fas fa-file-pdf"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" title="طباعة">
                    <i class="fas fa-print"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div id="searchResults">
                <!-- Results will be loaded here -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>التصنيف</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>المسؤول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="resultsBody">
                            {% for doc in results|default([]) %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        {{ doc.title }}
                                        {% if doc.highlight %}
                                        <small class="text-muted ms-2">(تطابق: {{ doc.highlight }})</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if doc.document_type == 'incoming' %}
                                    <span class="badge bg-primary">وارد</span>
                                    {% else %}
                                    <span class="badge bg-success">صادر</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.category }}</td>
                                <td>{{ doc.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if doc.status == 'pending' %}
                                    <span class="badge bg-warning">قيد المعالجة</span>
                                    {% elif doc.status == 'in_progress' %}
                                    <span class="badge bg-info">جاري المعالجة</span>
                                    {% elif doc.status == 'completed' %}
                                    <span class="badge bg-success">تم المعالجة</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.user.username }}</td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-info" 
                                                data-action="view" data-id="{{ doc.id }}" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-primary"
                                                data-action="download" data-id="{{ doc.id }}" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- No Results Message -->
                {% if not results|default(False) %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب تعديل معايير البحث</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');
    
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        document.getElementById('searchResults').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري البحث...</span>
                </div>
                <p class="mt-2">جاري البحث...</p>
            </div>
        `;

        // Collect form data
        const formData = new FormData(searchForm);
        // You would typically make an AJAX request here

        // For demo purposes, simulate a delay
        setTimeout(() => {
            // Replace with actual search results
            // This would typically be handled by your backend
        }, 1000);
    });

    // Handle document actions
    document.addEventListener('click', function(e) {
        const button = e.target.closest('[data-action]');
        if (!button) return;

        const action = button.dataset.action;
        const id = button.dataset.id;

        switch(action) {
            case 'view':
                // Handle document view
                break;
            case 'download':
                // Handle document download
                break;
        }
    });
});
</script>
{% endblock %}