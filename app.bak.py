from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, abort
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import Login<PERSON>anager, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, UTC
from flask_mail import Mail, Message
import os
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///documents.db'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['ALLOWED_EXTENSIONS'] = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'}

# تكوين إعدادات البريد الإلكتروني
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = os.environ.get('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.environ.get('MAIL_PASSWORD')

db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager(app)
login_manager.login_view = 'login'
mail = Mail(app)
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'

# Ensure upload directory exists
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    if request.method == 'POST':
        # Basic validation
        title = request.form.get('title')
        document_type = request.form.get('document_type')
        file = request.files.get('document_file')
        
        if not file or not title or not document_type:
            flash('يرجى تعبئة جميع الحقول المطلوبة', 'danger')
            return redirect(url_for('upload'))
        
        if not allowed_file(file.filename):
            flash('نوع الملف غير مسموح به', 'danger')
            return redirect(url_for('upload'))

        # Create folder structure
        dept_folder = os.path.join(app.config['UPLOAD_FOLDER'], str(current_user.department_id))
        type_folder = os.path.join(dept_folder, document_type)
        for folder in [dept_folder, type_folder]:
            if not os.path.exists(folder):
                os.makedirs(folder)
        
        # Save file
        filename = secure_filename(f"{uuid.uuid4()}_{file.filename}")
        file_path = os.path.join(type_folder, filename)
        file.save(file_path)
        
        # Create document
        document = Document(
            title=title,
            description=request.form.get('description'),
            document_type=document_type,
            file_path=os.path.join(str(current_user.department_id), document_type, filename),
            user_id=current_user.id,
            department_id=current_user.department_id
        )

        # Handle document type specific fields
        if document_type == 'outgoing':
            document.document_date = datetime.strptime(request.form.get('outgoing_date'), '%Y-%m-%d').date()
            document.document_number = request.form.get('outgoing_number')
            document.destination_department = request.form.get('destination_department')
            document.subject = request.form.get('outgoing_subject')
            
            # Save document first
            db.session.add(document)
            db.session.commit()

            # Try to send email to department
            recipient_email = document.recipient_department_email
            if recipient_email:
                try:
                    msg = Message(
                        f'مستند صادر جديد: {document.title}',
                        sender=app.config['MAIL_USERNAME'],
                        recipients=[recipient_email]
                    )
                    msg.body = f"""
مرحباً،

تم إرسال مستند صادر جديد إليكم من {current_user.department.name}.

تفاصيل المستند:
- العنوان: {document.title}
- رقم الكتاب: {document.document_number}
- التاريخ: {document.document_date}
- الموضوع: {document.subject}

المستند مرفق مع هذا البريد.
"""
                    with app.open_resource(os.path.join(app.config['UPLOAD_FOLDER'], document.file_path)) as fp:
                        msg.attach(
                            filename=os.path.basename(file.filename),
                            content_type=file.content_type,
                            data=fp.read()
                        )
                    mail.send(msg)
                    flash('تم إرسال المستند بنجاح إلى البريد الإلكتروني للقسم المعني', 'success')
                except Exception as e:
                    flash(f'تم حفظ المستند ولكن فشل إرسال البريد الإلكتروني: {str(e)}', 'warning')
        
        elif document_type == 'incoming':
            document.incoming_date = datetime.strptime(request.form.get('incoming_date'), '%Y-%m-%d').date()
            document.source_department = request.form.get('source_department')
            document.document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d').date()
            document.subject = request.form.get('subject')
            document.department_id = request.form.get('department_id') or current_user.department_id
            db.session.add(document)
            db.session.commit()

        create_activity(current_user.id, document.id, 'upload', f'تم رفع مستند {document_type} جديد: {title}')
        flash(f'تم رفع المستند {document_type} بنجاح', 'success')
        return redirect(url_for('documents'))
    
    departments = Department.query.all()
    return render_template('upload.html', departments=departments)