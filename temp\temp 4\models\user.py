"""
User model
"""
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, UTC
from .database import db

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)
    fullname = db.Column(db.String(120), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    role = db.Column(db.String(50), default='موظف')
    permissions = db.Column(db.String(200))
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(UTC))
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)

    # Define relationships
    department = db.relationship('Department', back_populates='users')
    activities = db.relationship('Activity', back_populates='user', lazy=True)
    documents = db.relationship('Document', back_populates='created_by', lazy=True)

    def has_permission(self, permission):
        """Check if user has a specific permission"""
        if self.role == 'مدير':
            return True
        if not self.permissions:
            return False
        return permission in self.permissions.split(',')

    def __repr__(self):
        return f'<User {self.username}>'