import os
import uuid
from datetime import datetime, UTC
from flask import (Flask, render_template, request, redirect, url_for, flash,
                  send_file, abort, send_from_directory, jsonify, make_response)
from sqlalchemy import or_ as db_or, func
from flask_login import login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from werkzeug.utils import secure_filename
from flask_mail import Mail, Message
from io import BytesIO

# Initialize Flask app
app = Flask(__name__)

# Configure app
app.config.update(
    SECRET_KEY=os.environ.get('SECRET_KEY', 'your-secret-key-here'),
    SQLALCHEMY_DATABASE_URI='sqlite:///documents.db',
    UPLOAD_FOLDER='uploads',
    MAX_CONTENT_LENGTH=16 * 1024 * 1024,  # 16MB max file size
    ALLOWED_EXTENSIONS={'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'},
    MAIL_SERVER='smtp.gmail.com',
    MAIL_PORT=587,
    MAIL_USE_TLS=True,
    MAIL_USERNAME=os.environ.get('MAIL_USERNAME'),
    MAIL_PASSWORD=os.environ.get('MAIL_PASSWORD')
)

# Initialize extensions first
from models import db, User, Department, Document, Activity
from flask_migrate import Migrate
from flask_login import LoginManager

# Initialize database
db.init_app(app)

# Initialize other extensions
migrate = Migrate(app, db)
login_manager = LoginManager(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
mail = Mail(app)

# Helper functions
def format_document_for_json(doc):
    """Format document for JSON response"""
    result = {
        'id': doc.id,
        'title': doc.title,
        'document_type': doc.document_type,
        'created_at': doc.created_at.strftime('%Y-%m-%d'),
        'user': doc.created_by.username,
        'department': doc.department.name,
        'description': doc.description,
        'subject': doc.subject,
        'document_number': doc.document_number,
        'document_date': doc.document_date.strftime('%Y-%m-%d') if doc.document_date else None,
        'is_owner': doc.user_id == current_user.id,
        'is_admin': current_user.role == 'مدير',
        'tags': doc.tags
    }

    # Add archive-specific fields
    if doc.document_type == 'archive':
        result.update({
            'archive_number': doc.archive_number,
            'archive_type': doc.archive_type
        })
    # Add incoming-specific fields
    elif doc.document_type == 'incoming':
        result.update({
            'incoming_date': doc.incoming_date.strftime('%Y-%m-%d') if doc.incoming_date else None,
            'source_department': doc.source_department
        })
    # Add outgoing-specific fields
    elif doc.document_type == 'outgoing':
        result.update({
            'destination_department': doc.destination_department
        })

    return result

def export_excel(results):
    """Export results to Excel file"""
    excel_file = create_excel_report(results)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'نتائج_البحث_{timestamp}.xlsx'
    
    # Ensure correct parameter alignment
    return send_file(
        excel_file,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=filename
    )

def export_statistics_excel(stats, date_from, date_to):
    """Export statistics to Excel"""
    wb = Workbook()
    ws = wb.active
    ws.title = "إحصائيات المستندات"
    ws.sheet_view.rightToLeft = True

    # Add title and date range
    ws['A1'] = 'إحصائيات المستندات'
    ws['A2'] = f'الفترة: من {date_from.strftime("%Y-%m-%d") if date_from else "البداية"} إلى {date_to.strftime("%Y-%m-%d") if date_to else "النهاية"}'

    # Document types statistics
    ws['A4'] = 'إحصائيات حسب النوع'
    ws['A5'] = 'وارد'
    ws['B5'] = stats['by_type']['incoming']
    ws['A6'] = 'صادر'
    ws['B6'] = stats['by_type']['outgoing']
    ws['A7'] = 'أرشيف'
    ws['B7'] = stats['by_type']['archive']
    ws['A8'] = 'المجموع'
    ws['B8'] = stats['total']

    # Department statistics
    ws['A10'] = 'إحصائيات حسب القسم'
    row = 11
    for dept, count in stats['by_department'].items():
        ws[f'A{row}'] = dept
        ws[f'B{row}'] = count
        row += 1

    # Save to BytesIO
    excel_file = BytesIO()
    wb.save(excel_file)
    excel_file.seek(0)
    
    return send_file(
        excel_file,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'إحصائيات_المستندات_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    )


# Create upload folder
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

def create_excel_report(results):
    """Create Excel file with proper Arabic support using openpyxl"""
    from io import BytesIO
    excel_file = BytesIO()
    
    workbook = Workbook()
    worksheet = workbook.active
    worksheet.title = 'نتائج البحث'
    worksheet.sheet_view.rightToLeft = True
    
    # Create styles
    header_font = Font(name='Times New Roman', size=12, bold=True)
    cell_font = Font(name='Times New Roman', size=11)
    alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    cell_alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
    fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Headers
    headers = [
        'العنوان', 'نوع المستند', 'تاريخ الانشاء', 'المسؤول', 'القسم',
        'الموضوع', 'الوصف', 'الوسوم', 'رقم المستند', 'تاريخ المستند',
        'رقم الأرشفة', 'نوع الأرشفة', 'تاريخ الاستلام', 'الجهة المرسلة', 'الجهة المرسل إليها'
    ]
    
    # Write headers
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col)
        cell.value = header
        cell.font = header_font
        cell.alignment = alignment
        cell.fill = fill
        cell.border = thin_border
        worksheet.column_dimensions[worksheet.cell(row=1, column=col).column_letter].width = 25
    
    # Write data
    for row, doc in enumerate(results, 2):
        doc_type = {
            'incoming': 'وارد',
            'outgoing': 'صادر',
            'archive': 'أرشيف'
        }.get(doc.document_type, '')
        
        row_data = [
            doc.title or '',
            doc_type,
            doc.created_at.strftime('%Y-%m-%d'),
            doc.created_by.username,
            doc.department.name,
            doc.subject or '',
            doc.description or '',
            doc.tags or '',
            doc.document_number or '',
            doc.document_date.strftime('%Y-%m-%d') if doc.document_date else '',
            doc.archive_number or '',
            doc.archive_type or '',
            doc.incoming_date.strftime('%Y-%m-%d') if doc.incoming_date else '',
            doc.source_department or '',
            doc.destination_department or ''
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = worksheet.cell(row=row, column=col)
            cell.value = value
            cell.font = cell_font
            cell.alignment = cell_alignment
            cell.border = thin_border
    
    workbook.save(excel_file)
    excel_file.seek(0)
    return excel_file

# Database initialization moved to init_app()

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@app.route('/')
@login_required
def index():
    """Dashboard route showing statistics and recent activities"""
    if current_user.role == 'مدير':
        # Admin sees all documents
        incoming_count = Document.query.filter_by(document_type='incoming').with_entities(Document.id).count()
        outgoing_count = Document.query.filter_by(document_type='outgoing').with_entities(Document.id).count()
        archive_count = Document.query.filter_by(document_type='archive').with_entities(Document.id).count()
        total_count = Document.query.with_entities(Document.id).count()
        recent_documents = Document.query.order_by(Document.created_at.desc()).limit(5).all()
        recent_activities = Activity.query.order_by(Activity.timestamp.desc()).limit(5).all()
    else:
        # Regular users only see their department's documents
        base_query = Document.query.filter_by(department_id=current_user.department_id)
        incoming_count = base_query.filter_by(document_type='incoming').with_entities(Document.id).count()
        outgoing_count = base_query.filter_by(document_type='outgoing').with_entities(Document.id).count()
        archive_count = base_query.filter_by(document_type='archive').with_entities(Document.id).count()
        total_count = base_query.with_entities(Document.id).count()
        recent_documents = base_query.order_by(Document.created_at.desc()).limit(5).all()
        recent_activities = Activity.query.filter_by(document_id=Document.id).\
            filter(Document.department_id==current_user.department_id).\
            order_by(Activity.timestamp.desc()).limit(5).all()
    
    return render_template('index.html',
                        incoming_count=incoming_count,
                        outgoing_count=outgoing_count,
                        archive_count=archive_count,
                        total_count=total_count,
                        recent_documents=recent_documents,
                        recent_activities=recent_activities)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password, password):
            login_user(user)
            user.last_login = datetime.now(UTC)
            db.session.commit()
            return redirect(url_for('index'))
        
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """Handle user logout"""
    logout_user()
    return redirect(url_for('login'))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def create_activity(user_id, document_id, action, description):
    """Create an activity log entry"""
    try:
        activity = Activity(
            user_id=user_id,
            document_id=document_id,
            action=action,
            action_description=description
        )
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        print(f"Error creating activity log: {str(e)}")
        db.session.rollback()

@app.route('/init')
def init_admin():
    """Initialize admin user and departments if they don't exist"""
    try:
        # Clean up existing departments
        Department.query.delete()
        db.session.commit()
        
        # Initialize departments
        departments = [
            {"name": "القسم الاداري", "code": "338"},
            {"name": "القسم الدولي", "code": "333"},
            {"name": "القسم القانوني", "code": "339"},
            {"name": "القسم المالي", "code": "340"},
            {"name": "القسم الهندسي", "code": "336"},
            {"name": "المدير العام", "code": "341"},
            {"name": "المدير العام\\ النظام الشامل المصرفي", "code": "379"},
            {"name": "المدير العام\\ وحدة غسيل اموال", "code": "316"},
            {"name": "المدير العام\\مكتب اعادة الهيكيلة", "code": "373"},
            {"name": "المدير العام\\مكتب مراقب الامتثال", "code": "2040"},
            {"name": "قسم ادارة المخاطر", "code": "374"},
            {"name": "قسم الائتمان", "code": "376"},
            {"name": "قسم الاحصاء", "code": "342"},
            {"name": "قسم البطاقات", "code": "540"},
            {"name": "قسم الحاسبة الالكترونية", "code": "309"},
            {"name": "قسم الخزينة", "code": "577"},
            {"name": "قسم الدراسات والعمليات المصرفية", "code": "306"},
            {"name": "قسم الرقابة", "code": "345"},
            {"name": "قسم الموارد البشرية", "code": "305"},
            {"name": "الخزينة الموحدة", "code": "718"},
            {"name": "امن المعلومات", "code": "000"}
        ]

        # Create departments
        for dept in departments:
            department = Department(
                name=dept["name"],
                code=dept["code"],
                email=f"dept{dept['code']}@rasheedbank.gov.iq"
            )
            db.session.add(department)
        db.session.commit()
        flash('تم إنشاء الأقسام بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء الأقسام: {str(e)}', 'danger')
        return redirect(url_for('login'))

    # Initialize admin user if no users exist
    if User.query.count() == 0:
        # Get the first department for the admin
        first_dept = Department.query.first()
        admin = User(
            username='admin',
            fullname='مدير النظام',
            email='<EMAIL>',
            role='مدير',
            department_id=first_dept.id if first_dept else None,
            permissions='وارد,صادر,أرشفة'  # Explicit permissions for consistency
        )
        admin.password = generate_password_hash('admin123')
        db.session.add(admin)
        db.session.commit()
        flash('تم إنشاء حساب المدير بنجاح', 'success')
        return redirect(url_for('login'))
    
    return 'تم إنشاء الحساب والأقسام مسبقاً'

@app.route('/document/<int:document_id>/<string:action>', methods=['GET', 'POST'])
@login_required
def document_action(document_id, action):
    """Handle document actions: view, download, edit, delete"""
    # Validate action parameter
    valid_actions = ['view', 'download', 'edit', 'delete']
    if action not in valid_actions:
        flash('إجراء غير صالح', 'danger')
        return redirect(url_for('documents'))

    try:
        document = Document.query.get_or_404(document_id)
        
        # Check if user has permission to access this document
        if current_user.role != 'مدير' and document.department_id != current_user.department_id:
            flash('ليس لديك صلاحية للوصول إلى هذا المستند', 'danger')
            abort(403)
        
        if document.document_type == 'incoming' and not current_user.has_permission('وارد'):
            flash('ليس لديك صلاحية للوصول إلى المستندات الواردة', 'danger')
            abort(403)
        elif document.document_type == 'outgoing' and not current_user.has_permission('صادر'):
            flash('ليس لديك صلاحية للوصول إلى المستندات الصادرة', 'danger')
            abort(403)

        if action == 'view':
            create_activity(current_user.id, document.id, 'view', 'تم عرض المستند')
            return render_template('edit_document.html', document=document, edit_mode=False)
            
        elif action == 'download':
            file_full_path = os.path.join(app.config['UPLOAD_FOLDER'], document.file_path)
            if not os.path.exists(file_full_path):
                flash('الملف غير موجود', 'danger')
                return redirect(url_for('documents'))
                
            create_activity(current_user.id, document.id, 'download', 'تم تحميل المستند')
            
            # Split the path to get directory and filename
            file_dir = os.path.dirname(document.file_path)
            file_name = os.path.basename(document.file_path)
            
            return send_from_directory(
                os.path.join(app.config['UPLOAD_FOLDER'], file_dir),
                file_name,
                as_attachment=True,
                download_name=file_name
            )
            
        elif action == 'edit':
            if current_user.role != 'مدير' and document.user_id != current_user.id:
                flash('ليس لديك صلاحية لتعديل هذا المستند', 'danger')
                abort(403)
                
            if request.method == 'POST':
                document.title = request.form.get('title')
                document.description = request.form.get('description')
                document.subject = request.form.get('subject')
                db.session.commit()
                create_activity(current_user.id, document.id, 'edit', 'تم تعديل المستند')
                flash('تم تحديث المستند بنجاح', 'success')
                return redirect(url_for('documents'))
                
            return render_template('edit_document.html', document=document, edit_mode=True)
            
        elif action == 'delete':
            if current_user.role != 'مدير' and document.user_id != current_user.id:
                flash('ليس لديك صلاحية لحذف هذا المستند', 'danger')
                abort(403)
                
            create_activity(current_user.id, document.id, 'delete', 'تم حذف المستند')
            if document.file_path:
                try:
                    # Remove file
                    file_full_path = os.path.join(app.config['UPLOAD_FOLDER'], document.file_path)
                    if os.path.exists(file_full_path):
                        os.remove(file_full_path)
                        
                        # Try to remove document type folder if empty
                        doc_type_folder = os.path.dirname(file_full_path)
                        if os.path.exists(doc_type_folder) and not os.listdir(doc_type_folder):
                            os.rmdir(doc_type_folder)
                            
                            # Try to remove department folder if empty
                            dept_folder = os.path.dirname(doc_type_folder)
                            if os.path.exists(dept_folder) and not os.listdir(dept_folder):
                                os.rmdir(dept_folder)
                                
                except Exception as e:
                    print(f"Error removing file or folders: {e}")
            # First delete associated activities
            Activity.query.filter_by(document_id=document.id).delete()
            # Then delete the document
            db.session.delete(document)
            db.session.commit()
            flash('تم حذف المستند بنجاح', 'success')
            return redirect(url_for('documents'))

    except Exception as e:
        db.session.rollback()
        print(f"Error in document action: {str(e)}")
        flash('حدث خطأ أثناء تنفيذ الإجراء: ' + str(e), 'danger')
        return redirect(url_for('documents'))

def init_db(app):
    """Initialize the database"""
    with app.app_context():
        # Drop all tables first
        db.drop_all()
        # Create all tables fresh
        db.create_all()
        print("   ✓ تم تهيئة قاعدة البيانات")
        return True

def init_app():
    """Initialize the application"""
    print("\nجاري تهيئة النظام...")
    print("-" * 40)

    try:
        # Step 1: Create required directories
        print("1. فحص المجلدات المطلوبة...")
        if not os.path.exists(app.config['UPLOAD_FOLDER']):
            os.makedirs(app.config['UPLOAD_FOLDER'])
            print("   ✓ تم إنشاء مجلد الملفات")
        else:
            print("   ✓ مجلد الملفات موجود")

        # Step 2: Initialize database
        print("\n2. تهيئة قاعدة البيانات...")
        with app.app_context():
            if init_db(app):
                print("   ✓ تم تهيئة قاعدة البيانات بنجاح")
            
            # Step 3: Check initial data
            print("\n3. فحص البيانات الأولية...")
            if User.query.count() == 0:
                print("   - قم بزيارة: http://localhost:5000/init")
                print("   - لإنشاء حساب المدير والأقسام")
            else:
                print("   ✓ البيانات موجودة ومهيأة")
        
        return True

    except Exception as e:
        print(f"\n✗ حدث خطأ أثناء تهيئة النظام: {str(e)}")
        return False

@app.route('/documents')
@login_required
def documents():
    """List all documents"""
    if current_user.role == 'مدير':
        # Admin can see all documents
        documents = Document.query.order_by(Document.created_at.desc()).all()
    else:
        # Regular users can only see their department's documents
        documents = Document.query.filter_by(department_id=current_user.department_id).order_by(Document.created_at.desc()).all()
    return render_template('documents.html', documents=documents)

@app.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('profile.html', user=current_user)

@app.route('/reports')
@login_required
def reports():
    """Reports page"""
    if current_user.role != 'مدير':
        flash('عذراً، هذه الصفحة متاحة للمدراء فقط', 'danger')
        return redirect(url_for('index'))
    departments = Department.query.all()
    return render_template('reports.html', departments=departments)

@app.route('/generate_report/<type>', methods=['POST'])
@login_required
def generate_report(type):
    """Generate report based on type"""
    if current_user.role != 'مدير':
        return jsonify({'error': 'غير مصرح'}), 403

    try:
        date_from = datetime.strptime(request.form['date_from'], '%Y-%m-%d') if request.form.get('date_from') else None
        date_to = datetime.strptime(request.form['date_to'], '%Y-%m-%d') if request.form.get('date_to') else None
        
        if type == 'statistics':
            return generate_statistics_report(date_from, date_to)
        elif type == 'activity':
            return generate_activity_report(date_from, date_to)
        elif type == 'department':
            department_id = request.form.get('department')
            return generate_department_report(department_id, date_from, date_to)
        else:
            raise ValueError('نوع تقرير غير صالح')

    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'danger')
        return redirect(url_for('reports'))

def generate_statistics_report(date_from, date_to):
    """Generate statistics report"""
    query = Document.query
    
    if date_from:
        query = query.filter(Document.created_at >= date_from)
    if date_to:
        query = query.filter(Document.created_at <= date_to)

    # Get statistics
    stats = {
        'total': query.count(),
        'by_type': {
            'incoming': query.filter_by(document_type='incoming').count(),
            'outgoing': query.filter_by(document_type='outgoing').count(),
            'archive': query.filter_by(document_type='archive').count()
        },
        'by_department': {
            dept.name: query.filter_by(department_id=dept.id).count()
            for dept in Department.query.all()
        }
    }

    return export_statistics_excel(stats, date_from, date_to)

def generate_activity_report(date_from, date_to):
    """Generate activity log report"""
    query = Activity.query.join(Document).join(User)
    
    if date_from:
        query = query.filter(Activity.timestamp >= date_from)
    if date_to:
        query = query.filter(Activity.timestamp <= date_to)

    activities = query.order_by(Activity.timestamp.desc()).all()
    return export_activity_excel(activities, date_from, date_to)

def generate_department_report(department_id, date_from, date_to):
    """Generate department summary report"""
    query = Document.query
    
    if department_id:
        query = query.filter_by(department_id=department_id)
    if date_from:
        query = query.filter(Document.created_at >= date_from)
    if date_to:
        query = query.filter(Document.created_at <= date_to)

    summary = {
        dept.name: {
            'total': query.filter_by(department_id=dept.id).count(),
            'by_type': {
                'incoming': query.filter_by(department_id=dept.id, document_type='incoming').count(),
                'outgoing': query.filter_by(department_id=dept.id, document_type='outgoing').count(),
                'archive': query.filter_by(department_id=dept.id, document_type='archive').count()
            }
        }
        for dept in Department.query.all() if not department_id or dept.id == int(department_id)
    }

    return export_excel_generic(summary, 'ملخص_الأقسام', headers=[
        'القسم', 'إجمالي المستندات', 'وارد', 'صادر', 'أرشيف'
    ])

def export_excel_generic(data, filename_prefix, headers=None):
    """Generic Excel export function"""
    wb = Workbook()
    ws = wb.active
    ws.sheet_view.rightToLeft = True

    # Add headers if provided
    if headers:
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

    # Add data based on its structure
    if isinstance(data, dict):
        # For department and statistics reports
        row = 2
        for key, value in data.items():
            if isinstance(value, dict):
                if 'total' in value and 'by_type' in value:
                    # Department summary format
                    ws.cell(row=row, column=1, value=key)
                    ws.cell(row=row, column=2, value=value['total'])
                    ws.cell(row=row, column=3, value=value['by_type']['incoming'])
                    ws.cell(row=row, column=4, value=value['by_type']['outgoing'])
                    ws.cell(row=row, column=5, value=value['by_type']['archive'])
            else:
                # Simple key-value format
                ws.cell(row=row, column=1, value=key)
                ws.cell(row=row, column=2, value=value)
            row += 1
    elif isinstance(data, list):
        # For activity log
        for row, item in enumerate(data, 2):
            if hasattr(item, 'timestamp'):
                # Activity log format
                ws.cell(row=row, column=1, value=item.timestamp.strftime('%Y-%m-%d %H:%M'))
                ws.cell(row=row, column=2, value=item.user.username)
                ws.cell(row=row, column=3, value=item.document.title)
                ws.cell(row=row, column=4, value=item.action)
                ws.cell(row=row, column=5, value=item.action_description)

    # Auto-adjust column widths
    for column_cells in ws.columns:
        length = max(len(str(cell.value)) for cell in column_cells)
        ws.column_dimensions[column_cells[0].column_letter].width = length + 2

    # Save to BytesIO
    excel_file = BytesIO()
    wb.save(excel_file)
    excel_file.seek(0)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    return send_file(
        excel_file,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'{filename_prefix}_{timestamp}.xlsx'
    )

def export_activity_excel(activities, date_from, date_to):
    """Export activity log to Excel"""
    return export_excel_generic(activities, 'سجل_النشاطات', headers=[
        'التاريخ', 'المستخدم', 'المستند', 'الإجراء', 'التفاصيل'
    ])

def export_statistics_excel(stats, date_from, date_to):
    """Export statistics to Excel"""
    return export_excel_generic(stats, 'إحصائيات_المستندات', headers=[
        'النوع', 'العدد'
    ])

@app.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    """Handle document upload"""
    if request.method == 'GET':
        departments = Department.query.all()
        return render_template('upload.html', departments=departments)
        
    # Handle POST request
    try:
        # Validate file upload
        if 'document_file' not in request.files:
            raise ValueError('لم يتم اختيار ملف')
            
        file = request.files['document_file']
        if file.filename == '':
            raise ValueError('لم يتم اختيار ملف')
            
        if not allowed_file(file.filename):
            raise ValueError('نوع الملف غير مسموح به')
            
        print(f"File validation successful: {file.filename}")

        # Get and validate basic document info
        department = Department.query.get(current_user.department_id)
        if not department:
            raise ValueError('القسم غير موجود')
            
        document_type = request.form.get('document_type')
        if not document_type:
            raise ValueError('نوع المستند مطلوب')
            
        print(f"Basic document info validated - Type: {document_type}, Department: {department.name}")

        # Prepare file path and folder
        folder_path = os.path.join(app.config['UPLOAD_FOLDER'], department.code, document_type)
        if not os.path.exists(folder_path):
            print(f"Creating folder: {folder_path}")
            os.makedirs(folder_path, exist_ok=True)
            
        # Secure the filename and generate unique name
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(department.code, document_type, unique_filename)
        full_path = os.path.join(app.config['UPLOAD_FOLDER'], file_path)
        
        # Save the file
        print(f"Saving file to: {full_path}")
        file.save(full_path)
        print("File saved successfully")

        # Process document dates
        try:
            if document_type == 'incoming':
                if not all([request.form.get('document_date'), request.form.get('incoming_date')]):
                    raise ValueError('تواريخ المستند الوارد مطلوبة')
                document_date = datetime.strptime(request.form.get('document_date'), '%Y-%m-%d').date()
                incoming_date = datetime.strptime(request.form.get('incoming_date'), '%Y-%m-%d').date()
            elif document_type == 'outgoing':
                if not request.form.get('outgoing_date'):
                    raise ValueError('تاريخ الصادر مطلوب')
                document_date = datetime.strptime(request.form.get('outgoing_date'), '%Y-%m-%d').date()
            elif document_type == 'archive':
                document_date = datetime.now(UTC).date()
        except ValueError as e:
            if os.path.exists(full_path):
                os.remove(full_path)
            raise ValueError('صيغة التاريخ غير صحيحة')

        try:
            # Get and normalize form fields
            subject = request.form.get('subject') or request.form.get(f'{document_type}_subject')
            if not subject:
                raise ValueError('موضوع المستند مطلوب')

            # Create document record
            document = Document(
                title=subject,
                description=request.form.get('description'),
                subject=subject,
                document_type=document_type,
                file_path=file_path,
                user_id=current_user.id,
                department_id=current_user.department_id,
                document_number=request.form.get('document_number'),
                document_date=document_date,
                tags=request.form.get('tags')
            )

            # Set type-specific fields
            if document_type == 'incoming':
                document.incoming_date = incoming_date
                document.source_department = request.form.get('source_department')
            elif document_type == 'outgoing':
                document.destination_department = request.form.get('destination_department')
            elif document_type == 'archive':
                document.archive_number = request.form.get('archive_number')
                document.archive_type = request.form.get('archive_type')

            # Validate and save
            print("Validating document...")
            document.validate()
            print(f"Document validated successfully: {document.subject}")
            
            print("Adding document to database...")
            db.session.add(document)
            db.session.commit()
            print(f"Document saved with ID: {document.id}")
            
            create_activity(current_user.id, document.id, 'upload', f'تم رفع مستند {document_type} جديد')

            # Send email notification for outgoing documents
            if document_type == 'outgoing' and document.recipient_department_email:
                try:
                    msg = Message(
                        f'مستند جديد: {document.title}',
                        sender=app.config['MAIL_USERNAME'],
                        recipients=[document.recipient_department_email]
                    )
                    
                    msg.body = f'''تم إرسال مستند جديد إلى قسمكم:

العنوان: {document.title}
الموضوع: {document.subject}
رقم المستند: {document.document_number}
التاريخ: {document.document_date}
من: {document.department.name}

يمكنكم الاطلاع على المستند من خلال نظام إدارة المستندات.
'''
                    mail.send(msg)
                    flash('تم رفع المستند وإرسال الإشعار بنجاح', 'success')
                except Exception as e:
                    print(f"Error sending email: {str(e)}")
                    flash('تم رفع المستند لكن فشل إرسال الإشعار: ' + str(e), 'warning')
            else:
                flash('تم رفع المستند بنجاح', 'success')

            return redirect(url_for('documents'))
            
        except ValueError as ve:
            if os.path.exists(full_path):
                os.remove(full_path)
                print(f"Removed file due to validation error: {full_path}")
            print(f"Validation error: {str(ve)}")
            flash(str(ve), 'danger')
            return redirect(request.url)
            
        except Exception as e:
            if os.path.exists(full_path):
                os.remove(full_path)
                print(f"Removed file due to error: {full_path}")
            db.session.rollback()
            print(f"Error saving document: {str(e)}")
            flash('حدث خطأ أثناء حفظ المستند', 'danger')
            return redirect(request.url)

    except ValueError as ve:
        flash(str(ve), 'danger')
        return redirect(request.url)
    except Exception as e:
        print(f"Error in document upload: {str(e)}")
        print(f"Document type: {document_type if 'document_type' in locals() else 'unknown'}")
        print(f"Form data: {request.form}")
        flash(f'فشل رفع المستند: {str(e)}', 'danger')
        return redirect(request.url)
        

def get_search_results(query_params, user):
    """Get search results based on filters"""
    query = Document.query
    
    # Text search
    if text := query_params.get('query', '').strip():
        text_filter = db_or(
            Document.title.ilike(f'%{text}%'),
            Document.description.ilike(f'%{text}%'),
            Document.subject.ilike(f'%{text}%'),
            Document.tags.ilike(f'%{text}%')
        )
        query = query.filter(text_filter)
    
    # Document type filter
    if doc_type := query_params.get('document_type'):
        query = query.filter(Document.document_type == doc_type)
    
    # User filter
    if user_id := query_params.get('user'):
        query = query.filter(Document.user_id == int(user_id))
    
    # Date range filter
    if date_from := query_params.get('date_from'):
        query = query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
    if date_to := query_params.get('date_to'):
        query = query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
    
    # Department restriction for non-admin users
    if user.role != 'مدير':
        query = query.filter(Document.department_id == user.department_id)
    
    return query.order_by(Document.created_at.desc()).all()

@app.route('/search')
@login_required
def search():
    """Search documents with filters and export"""
    users = User.query.all()
    
    if not request.args:
        return render_template('search.html', users=users)
    
    try:
        results = get_search_results(request.args, current_user)
        
        # Handle Excel export
        if request.args.get('export') == 'excel':
            return export_excel(results)
        
        # Handle AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify([{
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'created_at': doc.created_at.strftime('%Y-%m-%d'),
                'user': doc.created_by.username,
                'department': doc.department.name,
                'description': doc.description,
                'is_owner': doc.user_id == current_user.id,
                'is_admin': current_user.role == 'مدير'
            } for doc in results])
        
        return render_template('search.html', results=results, users=users)
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)

@app.route('/search/download')
@login_required
def download_results():
    """Download search results as Excel"""
    try:
        # Get search results
        results = get_search_results(request.args, current_user)
        
        # Check if there are any results
        if not results:
            return jsonify({
                'status': 'error',
                'message': 'لا توجد نتائج للتصدير'
            }), 404

        # Create Excel report
        excel_data = create_excel_report(results)
        
        # Set response headers
        headers = {
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': f'attachment; filename=نتائج_البحث_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
        
        return make_response(excel_data.getvalue()), 200, headers
        
    except Exception as e:
        print(f"Error in download_results: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': 'حدث خطأ أثناء تصدير النتائج'
        }), 500
    except Exception as e:
        flash(str(e), 'danger')
        return redirect(url_for('search'))
        filename = f'نتائج_البحث_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        resp = make_response(excel.getvalue())
        resp.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        resp.headers['Content-Disposition'] = f'attachment; filename={filename}'
        return resp
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء ملف Excel: {str(e)}', 'danger')
        return redirect(url_for('search'))


            
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return handle_ajax_response(results)
        
        return render_template('search.html', results=results, users=users)
    
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)
    """Advanced document search with filters and export"""
    users = User.query.all()
    
    if not request.args:
        return render_template('search.html', users=users)
        
    try:
        query = Document.query
        
        # Apply text search
        if search_text := request.args.get('query', '').strip():
            text_filter = db_or(
                Document.title.ilike(f'%{search_text}%'),
                Document.description.ilike(f'%{search_text}%'),
                Document.subject.ilike(f'%{search_text}%'),
                Document.tags.ilike(f'%{search_text}%')
            )
            query = query.filter(text_filter)
            
        # Apply other filters
        if doc_type := request.args.get('document_type'):
            query = query.filter(Document.document_type == doc_type)
            
        if user_id := request.args.get('user'):
            query = query.filter(Document.user_id == int(user_id))
            
        if date_from := request.args.get('date_from'):
            query = query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
            
        if date_to := request.args.get('date_to'):
            query = query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
            
        # Department restriction
        if current_user.role != 'مدير':
            query = query.filter(Document.department_id == current_user.department_id)
            
        results = query.order_by(Document.created_at.desc()).all()
        
        # Excel export
        if request.args.get('export') == 'excel':
            try:
                excel_file = create_excel_report(results)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'نتائج_البحث_{timestamp}.xlsx'
                return send_file(excel_file,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=filename)
            except Exception as e:
                flash(f'حدث خطأ أثناء إنشاء ملف Excel: {str(e)}', 'danger')
                return redirect(url_for('search'))
                
        # AJAX response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify([{
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'created_at': doc.created_at.strftime('%Y-%m-%d'),
                'user': doc.created_by.username,
                'department': doc.department.name,
                'description': doc.description,
                'is_owner': doc.user_id == current_user.id,
                'is_admin': current_user.role == 'مدير'
            } for doc in results])
            
        # Normal response
        return render_template('search.html', results=results, users=users)
        
    except Exception as e:
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)
        
        # Apply filters
        if search_text := request.args.get('query', '').strip():
            if request.args.get('case_sensitive') == 'on':
                text_filter = db_or(
                    Document.title.like(f'%{search_text}%'),
                    Document.description.like(f'%{search_text}%'),
                    Document.subject.like(f'%{search_text}%'),
                    Document.tags.like(f'%{search_text}%')
                )
            else:
                text_filter = db_or(
                    Document.title.ilike(f'%{search_text}%'),
                    Document.description.ilike(f'%{search_text}%'),
                    Document.subject.ilike(f'%{search_text}%'),
                    Document.tags.ilike(f'%{search_text}%')
                )
            query = query.filter(text_filter)
        
        if doc_type := request.args.get('document_type'):
            query = query.filter(Document.document_type == doc_type)
        
        if user_id := request.args.get('user'):
            query = query.filter(Document.user_id == int(user_id))
        
        if date_from := request.args.get('date_from'):
            query = query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to := request.args.get('date_to'):
            query = query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        if tags := request.args.get('tags', '').strip():
            for tag in [t.strip() for t in tags.split(',') if t.strip()]:
                query = query.filter(Document.tags.like(f'%{tag}%'))
        
        # Regular users can only see their department's documents
        if current_user.role != 'مدير':
            query = query.filter(Document.department_id == current_user.department_id)
        
        # Get results
        results = query.order_by(Document.created_at.desc()).all()
        
        # Excel export
        if request.args.get('export') == 'excel':
            try:
                excel_file = create_excel_report(results)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'نتائج_البحث_{timestamp}.xlsx'
                return send_file(
                    excel_file,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=filename
                )
            except Exception as e:
                flash(f'حدث خطأ أثناء إنشاء ملف Excel: {str(e)}', 'danger')
                return redirect(url_for('search'))
        
        # AJAX response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify([{
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'created_at': doc.created_at.strftime('%Y-%m-%d'),
                'user': doc.created_by.username,
                'department': doc.department.name,
                'description': doc.description,
                'is_owner': doc.user_id == current_user.id,
                'is_admin': current_user.role == 'مدير'
            } for doc in results])
        
        # Normal response
        return render_template('search.html', results=results, users=users)
        
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)
        
        # Apply text search
        search_text = request.args.get('query', '').strip()
        if search_text:
            if request.args.get('case_sensitive') == 'on':
                text_filter = db_or(
                    Document.title.like(f'%{search_text}%'),
                    Document.description.like(f'%{search_text}%'),
                    Document.subject.like(f'%{search_text}%'),
                    Document.tags.like(f'%{search_text}%')
                )
            else:
                text_filter = db_or(
                    Document.title.ilike(f'%{search_text}%'),
                    Document.description.ilike(f'%{search_text}%'),
                    Document.subject.ilike(f'%{search_text}%'),
                    Document.tags.ilike(f'%{search_text}%')
                )
            query = query.filter(text_filter)
        
        # Filter by document type
        doc_type = request.args.get('document_type')
        if doc_type:
            query = query.filter(Document.document_type == doc_type)
        
        # Filter by user
        user_id = request.args.get('user')
        if user_id:
            query = query.filter(Document.user_id == int(user_id))
        
        # Filter by date range
        date_from = request.args.get('date_from')
        if date_from:
            query = query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        date_to = request.args.get('date_to')
        if date_to:
            query = query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        # Filter by tags
        tags = request.args.get('tags', '').strip()
        if tags:
            for tag in [t.strip() for t in tags.split(',') if t.strip()]:
                query = query.filter(Document.tags.like(f'%{tag}%'))
        
        # Regular users can only see their department's documents
        if current_user.role != 'مدير':
            query = query.filter(Document.department_id == current_user.department_id)
        
        # Execute query
        results = query.order_by(Document.created_at.desc()).all()
        
        # Handle different response types
        if request.args.get('export') == 'excel':
            try:
                excel_file = create_excel_report(results)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f'نتائج_البحث_{timestamp}.xlsx'
                return send_file(
                    excel_file,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    as_attachment=True,
                    download_name=filename
                )
            except Exception as e:
                flash(f'حدث خطأ أثناء إنشاء ملف Excel: {str(e)}', 'danger')
                return redirect(url_for('search'))
        
        elif request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify([{
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'created_at': doc.created_at.strftime('%Y-%m-%d'),
                'user': doc.created_by.username,
                'department': doc.department.name,
                'description': doc.description,
                'is_owner': doc.user_id == current_user.id,
                'is_admin': current_user.role == 'مدير'
            } for doc in results])
        
        # Regular HTML response
        return render_template('search.html', results=results, users=users)
        
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)
        
        # Apply text search
        search_text = request.args.get('query', '').strip()
        if search_text:
            # Case sensitive or insensitive search based on parameter
            if request.args.get('case_sensitive') == 'on':
                text_filter = db_or(
                    Document.title.like(f'%{search_text}%'),
                    Document.description.like(f'%{search_text}%'),
                    Document.subject.like(f'%{search_text}%'),
                    Document.tags.like(f'%{search_text}%')
                )
            else:
                text_filter = db_or(
                    Document.title.ilike(f'%{search_text}%'),
                    Document.description.ilike(f'%{search_text}%'),
                    Document.subject.ilike(f'%{search_text}%'),
                    Document.tags.ilike(f'%{search_text}%')
                )
            query = query.filter(text_filter)
        
        # Filter by document type
        doc_type = request.args.get('document_type')
        if doc_type:
            query = query.filter(Document.document_type == doc_type)
        
        # Filter by user
        user_id = request.args.get('user')
        if user_id:
            query = query.filter(Document.user_id == int(user_id))
        
        # Filter by date range
        date_from = request.args.get('date_from')
        if date_from:
            query = query.filter(Document.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        date_to = request.args.get('date_to')
        if date_to:
            query = query.filter(Document.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        # Filter by tags
        tags = request.args.get('tags', '').strip()
        if tags:
            for tag in [t.strip() for t in tags.split(',') if t.strip()]:
                query = query.filter(Document.tags.like(f'%{tag}%'))
        
        # Regular users can only see their department's documents
        if current_user.role != 'مدير':
            query = query.filter(Document.department_id == current_user.department_id)
        
        # Execute query and get results
        results = query.order_by(Document.created_at.desc()).all()

        # Handle export request
        if request.args.get('export') == 'excel':
            excel_file = create_excel_report(results)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'نتائج_البحث_{timestamp}.xlsx'
            return send_file(excel_file,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=filename)
        # Handle AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify([{
                'id': doc.id,
                'title': doc.title,
                'document_type': doc.document_type,
                'created_at': doc.created_at.strftime('%Y-%m-%d'),
                'user': doc.created_by.username,
                'department': doc.department.name,
                'description': doc.description,
                'is_owner': doc.user_id == current_user.id,
                'is_admin': current_user.role == 'مدير'
            } for doc in results])

        # Regular request
        return render_template('search.html', results=results, users=users)

    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)

        return render_template('search.html', results=results, users=users)
        
    except Exception as e:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'error': str(e)}), 500
        flash(f'حدث خطأ أثناء البحث: {str(e)}', 'danger')
        return render_template('search.html', users=users)

@app.route('/departments')
@login_required
def departments():
    """Manage departments"""
    if current_user.role != 'مدير':
        abort(403)
    departments = Department.query.all()
    return render_template('departments.html', departments=departments)

@app.route('/add_department', methods=['POST'])
@login_required
def add_department():
    """Add a new department"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        department = Department(
            name=request.form['name'],
            code=request.form['code'],
            email=request.form['email']
        )
        db.session.add(department)
        db.session.commit()
        flash('تم إضافة القسم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل إضافة القسم: {str(e)}', 'danger')
    
    return redirect(url_for('departments'))

@app.route('/edit_department', methods=['POST'])
@login_required
def edit_department():
    """Edit an existing department"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        department = Department.query.get_or_404(request.form['department_id'])
        department.name = request.form['name']
        department.code = request.form['code']
        department.email = request.form['email']
        db.session.commit()
        flash('تم تحديث القسم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل تحديث القسم: {str(e)}', 'danger')
    
    return redirect(url_for('departments'))

@app.route('/delete_department/<int:department_id>')
@login_required
def delete_department(department_id):
    """Delete a department"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        department = Department.query.get_or_404(department_id)
        if department.documents:
            flash('لا يمكن حذف القسم لوجود مستندات مرتبطة به', 'danger')
        else:
            db.session.delete(department)
            db.session.commit()
            flash('تم حذف القسم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل حذف القسم: {str(e)}', 'danger')
    
    return redirect(url_for('departments'))

@app.route('/users')
@login_required
def users():
    """Manage users"""
    if current_user.role != 'مدير':
        abort(403)
    users = User.query.all()
    departments = Department.query.all()
    return render_template('users.html', users=users, departments=departments)

@app.route('/add_user', methods=['POST'])
@login_required
def add_user():
    """Add a new user"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        user = User(
            username=request.form['username'],
            fullname=request.form['fullname'],
            email=request.form['email'],
            department_id=request.form['department_id'],
            role=request.form['role'],
            permissions=','.join(request.form.getlist('permissions')),
            is_active=True
        )
        user.password = generate_password_hash(request.form['password'])
        db.session.add(user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل إضافة المستخدم: {str(e)}', 'danger')
    
    return redirect(url_for('users'))

@app.route('/edit_user', methods=['POST'])
@login_required
def edit_user():
    """Edit an existing user"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        user = User.query.get_or_404(request.form['user_id'])
        user.username = request.form['username']
        user.fullname = request.form['fullname']
        user.email = request.form['email']
        user.department_id = request.form['department_id']
        user.role = request.form['role']
        user.permissions = ','.join(request.form.getlist('permissions'))
        user.is_active = 'is_active' in request.form
        db.session.commit()
        flash('تم تحديث المستخدم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل تحديث المستخدم: {str(e)}', 'danger')
    
    return redirect(url_for('users'))

@app.route('/delete_user/<int:user_id>')
@login_required
def delete_user(user_id):
    """Delete a user"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        user = User.query.get_or_404(user_id)
        if user.id == current_user.id:
            flash('لا يمكن حذف المستخدم الحالي', 'danger')
        else:
            db.session.delete(user)
            db.session.commit()
            flash('تم حذف المستخدم بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل حذف المستخدم: {str(e)}', 'danger')
    
    return redirect(url_for('users'))

@app.route('/reset_password/<int:user_id>')
@login_required
def reset_password(user_id):
    """Reset user's password"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        user = User.query.get_or_404(user_id)
        new_password = 'password123'  # Default reset password
        user.password = generate_password_hash(new_password)
        db.session.commit()
        flash(f'تم إعادة تعيين كلمة المرور للمستخدم {user.username} إلى: {new_password}', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'فشل إعادة تعيين كلمة المرور: {str(e)}', 'danger')
    
    return redirect(url_for('users'))

@app.route('/email_settings')
@login_required
def email_settings():
    """Email settings page"""
    if current_user.role != 'مدير':
        abort(403)
    return render_template('email_settings.html', config=app.config)

@app.route('/save_email_settings', methods=['POST'])
@login_required
def save_email_settings():
    """Save email settings"""
    if current_user.role != 'مدير':
        abort(403)
    
    try:
        # Update app config
        app.config['MAIL_SERVER'] = request.form['mail_server']
        app.config['MAIL_PORT'] = int(request.form['mail_port'])
        app.config['MAIL_USE_TLS'] = 'mail_use_tls' in request.form
        app.config['MAIL_USERNAME'] = request.form['mail_username']
        app.config['MAIL_PASSWORD'] = request.form['mail_password']
        
        # Reinitialize mail extension with new settings
        mail.init_app(app)
        
        flash('تم حفظ إعدادات البريد الإلكتروني بنجاح', 'success')
    except Exception as e:
        flash(f'فشل حفظ الإعدادات: {str(e)}', 'danger')
    
    return redirect(url_for('email_settings'))

@app.route('/test_email_settings', methods=['POST'])
@login_required
def test_email_settings():
    """Test email settings"""
    if current_user.role != 'مدير':
        return jsonify({'success': False, 'error': 'غير مصرح'})
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'البيانات غير مكتملة'})

        # Create temporary mail configuration
        temp_config = {
            'MAIL_SERVER': data.get('mail_server'),
            'MAIL_PORT': int(data.get('mail_port')),
            'MAIL_USE_TLS': bool(data.get('mail_use_tls')),
            'MAIL_USERNAME': data.get('mail_username'),
            'MAIL_PASSWORD': data.get('mail_password')
        }

        # Create temporary Flask app for testing
        test_app = Flask('test_app')
        test_app.config.update(temp_config)

        # Initialize mail with test config
        with test_app.app_context():
            test_mail = Mail(test_app)
            msg = Message(
                'اختبار إعدادات البريد',
                sender=data.get('mail_username'),
                recipients=[data.get('mail_username')]
            )
            msg.body = f'''هذه رسالة اختبار للتحقق من إعدادات البريد الإلكتروني

الإعدادات المستخدمة:
- خادم SMTP: {data.get('mail_server')}
- المنفذ: {data.get('mail_port')}
- TLS: {'نعم' if data.get('mail_use_tls') else 'لا'}
- البريد الإلكتروني: {data.get('mail_username')}'''

            test_mail.send(msg)

        return jsonify({
            'success': True,
            'message': 'تم إرسال بريد الاختبار بنجاح'
        })

    except ValueError as ve:
        return jsonify({
            'success': False,
            'error': 'خطأ في تنسيق البيانات: ' + str(ve)
        })
    except Exception as e:
        error_msg = str(e).lower()
        if 'authentication' in error_msg:
            msg = 'خطأ في المصادقة: تأكد من اسم المستخدم وكلمة المرور'
        elif 'connection' in error_msg or 'network' in error_msg:
            msg = 'خطأ في الاتصال: تأكد من عنوان الخادم والمنفذ'
        elif 'timeout' in error_msg:
            msg = 'انتهت مهلة الاتصال: تأكد من إعدادات الشبكة'
        else:
            msg = 'حدث خطأ: ' + str(e)
        
        return jsonify({
            'success': False,
            'error': msg
        })


def send_outgoing_document_email(document):
    """Send email notification for outgoing document"""
    try:
        if not document.recipient_department_email:
            return False
            
        msg = Message(
            f'مستند جديد: {document.title}',
            sender=app.config['MAIL_USERNAME'],
            recipients=[document.recipient_department_email]
        )
        
        msg.body = f'''تم إرسال مستند جديد إلى قسمكم:

العنوان: {document.title}
الموضوع: {document.subject}
التاريخ: {document.document_date}
رقم المستند: {document.document_number}

يمكنكم الاطلاع على المستند من خلال نظام إدارة المستندات.
'''
        
        mail.send(msg)
        return True
        
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("نظام إدارة الوثائق - مصرف الرشيد")
    print("=" * 60)
    
    # Initialize the application
    if init_app():
        print("\nتعليمات التشغيل:")
        print("-" * 30)
        print("1. في أول تشغيل:")
        print("   - قم بزيارة: http://localhost:5000/init")
        print("   - سيتم إنشاء حساب المدير والأقسام")
        print("\n2. تسجيل الدخول:")
        print("   - اسم المستخدم: admin")
        print("   - كلمة المرور: admin123")
        print("\nملاحظات هامة:")
        print("- يجب تشغيل /init مرة واحدة فقط عند بدء النظام لأول مرة")
        print("- تأكد من وجود الصلاحيات الكافية لإنشاء المجلدات وقاعدة البيانات")
        print("=" * 60)
        
        # Run the application
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("\nفشل تهيئة النظام - راجع الأخطاء السابقة")
        print("=" * 60)